import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/card.dart';
import '../models/player.dart';
import '../models/game_state.dart';
import '../services/deck_service.dart';
import '../services/audio_service.dart';

class GameProvider extends ChangeNotifier {
  GameState _gameState = GameState.initial();
  
  GameState get gameState => _gameState;
  
  // 便捷访问器
  Player get dealer => _gameState.dealer;
  Player get humanPlayer => _gameState.humanPlayer;
  GamePhase get phase => _gameState.phase;
  int get balance => _gameState.balance;
  bool get canBet => _gameState.canBet;
  bool get canStartGame => _gameState.canStartGame;
  int get remainingCards => _gameState.remainingCards;

  // 初始化游戏
  Future<void> initializeGame() async {
    await _loadBalance();
    await _createNewDeck();
    _updateGameState(phase: GamePhase.betting);
    notifyListeners();
  }

  // 创建新牌组
  Future<void> _createNewDeck() async {
    final deck = DeckService.createMultipleDeck(_gameState.settings.deckCount);
    final shuffledDeck = DeckService.riffleShuffle(deck);
    
    _updateGameState(
      deck: shuffledDeck,
      discardPile: [],
      isShuffling: false,
    );
  }

  // 下注
  Future<void> placeBet(int amount) async {
    if (!canBet || amount > balance || amount < _gameState.settings.minBet) {
      return;
    }

    await SoundManager.playChipPlace();
    
    final newBalance = balance - amount;
    final updatedPlayer = humanPlayer.copyWith(
      bet: humanPlayer.bet + amount,
      isActive: true,
    );

    _updateGameState(
      balance: newBalance,
      humanPlayer: updatedPlayer,
    );
    
    await _saveBalance();
    notifyListeners();
  }

  // 清除下注
  Future<void> clearBet() async {
    if (!canBet) return;

    final refundAmount = humanPlayer.bet;
    final newBalance = balance + refundAmount;
    final updatedPlayer = humanPlayer.copyWith(bet: 0, isActive: false);

    _updateGameState(
      balance: newBalance,
      humanPlayer: updatedPlayer,
    );
    
    await _saveBalance();
    notifyListeners();
  }

  // 开始新游戏
  Future<void> startNewGame() async {
    if (!canStartGame) return;

    // 检查是否需要洗牌
    if (_gameState.needsShuffle) {
      await _shuffleDeck();
    }

    _updateGameState(
      phase: GamePhase.dealing,
      statusMessage: 'Dealing cards...',
    );
    notifyListeners();

    // 清空玩家手牌
    final clearedDealer = dealer.copyWith(cards: []);
    final clearedPlayer = humanPlayer.copyWith(cards: []);
    
    _updateGameState(
      dealer: clearedDealer,
      humanPlayer: clearedPlayer,
    );

    // 发牌序列
    await _dealInitialCards();
  }

  // 发初始牌
  Future<void> _dealInitialCards() async {
    final List<PlayingCard> newDeck = List.from(_gameState.deck);
    
    // 玩家第一张牌
    final playerCard1 = DeckService.dealCard(newDeck);
    if (playerCard1 != null) {
      await SoundManager.playCardDeal();
      final updatedPlayer = humanPlayer.copyWith(
        cards: [...humanPlayer.cards, playerCard1],
      );
      _updateGameState(deck: newDeck, humanPlayer: updatedPlayer);
      notifyListeners();
      await Future.delayed(const Duration(milliseconds: 500));
    }

    // 庄家第一张牌
    final dealerCard1 = DeckService.dealCard(newDeck);
    if (dealerCard1 != null) {
      await SoundManager.playCardDeal();
      final updatedDealer = dealer.copyWith(
        cards: [...dealer.cards, dealerCard1],
      );
      _updateGameState(deck: newDeck, dealer: updatedDealer);
      notifyListeners();
      await Future.delayed(const Duration(milliseconds: 500));
    }

    // 玩家第二张牌
    final playerCard2 = DeckService.dealCard(newDeck);
    if (playerCard2 != null) {
      await SoundManager.playCardDeal();
      final updatedPlayer = humanPlayer.copyWith(
        cards: [...humanPlayer.cards, playerCard2],
      );
      _updateGameState(deck: newDeck, humanPlayer: updatedPlayer);
      notifyListeners();
      await Future.delayed(const Duration(milliseconds: 500));
    }

    // 庄家第二张牌（隐藏）
    final dealerCard2 = DeckService.dealCard(newDeck);
    if (dealerCard2 != null) {
      await SoundManager.playCardDeal();
      final hiddenCard = dealerCard2.copyWithHidden(true);
      final updatedDealer = dealer.copyWith(
        cards: [...dealer.cards, hiddenCard],
      );
      _updateGameState(deck: newDeck, dealer: updatedDealer);
      notifyListeners();
      await Future.delayed(const Duration(milliseconds: 500));
    }

    // 检查保险和21点
    await _checkInsuranceAndBlackjack();
  }

  // 检查保险和21点
  Future<void> _checkInsuranceAndBlackjack() async {
    // 检查是否提供保险
    if (_gameState.shouldOfferInsurance) {
      _updateGameState(
        insuranceOffered: true,
        statusMessage: 'Insurance available - Dealer shows Ace',
      );
      notifyListeners();
      return;
    }

    // 检查21点
    if (humanPlayer.isBlackjack || dealer.isBlackjack) {
      await _handleBlackjackSituation();
    } else {
      _startPlayerTurn();
    }
  }

  // 处理21点情况
  Future<void> _handleBlackjackSituation() async {
    // 翻开庄家隐藏牌
    await _revealDealerHiddenCard();
    
    if (humanPlayer.isBlackjack && dealer.isBlackjack) {
      // 双方都是21点，平局
      await _endGameWithPush();
    } else if (humanPlayer.isBlackjack) {
      // 玩家21点，庄家不是
      await _endGameWithPlayerBlackjack();
    } else {
      // 庄家21点，玩家不是
      await _endGameWithDealerBlackjack();
    }
  }

  // 开始玩家回合
  void _startPlayerTurn() {
    _updateGameState(
      phase: GamePhase.playing,
      statusMessage: 'Your turn - Hit or Stand?',
    );
    notifyListeners();
  }

  // 玩家要牌
  Future<void> hit() async {
    if (phase != GamePhase.playing) return;

    final List<PlayingCard> newDeck = List.from(_gameState.deck);
    final newCard = DeckService.dealCard(newDeck);
    
    if (newCard != null) {
      await SoundManager.playCardDeal();
      
      final updatedPlayer = humanPlayer.copyWith(
        cards: [...humanPlayer.cards, newCard],
      );
      
      _updateGameState(
        deck: newDeck,
        humanPlayer: updatedPlayer,
      );
      notifyListeners();

      // 检查是否爆牌或21点
      if (updatedPlayer.isBust) {
        await _endGameWithPlayerBust();
      } else if (updatedPlayer.currentScore == 21) {
        await _stand();
      } else {
        _updateGameState(statusMessage: 'Hit or Stand?');
        notifyListeners();
      }
    }
  }

  // 玩家停牌
  Future<void> stand() async {
    if (phase != GamePhase.playing) return;
    await _startDealerTurn();
  }

  // 加倍
  Future<void> doubleDown() async {
    if (phase != GamePhase.playing || !humanPlayer.canDouble || balance < humanPlayer.bet) {
      return;
    }

    await SoundManager.playChipPlace();
    
    // 扣除额外下注
    final additionalBet = humanPlayer.bet;
    final newBalance = balance - additionalBet;
    
    final updatedPlayer = humanPlayer.copyWith(
      bet: humanPlayer.bet * 2,
    );

    _updateGameState(
      balance: newBalance,
      humanPlayer: updatedPlayer,
    );
    
    await _saveBalance();
    
    // 发一张牌后自动停牌
    await hit();
    
    if (phase == GamePhase.playing) {
      await _stand();
    }
  }

  // 分牌
  Future<void> split() async {
    if (phase != GamePhase.playing || !humanPlayer.canSplit || balance < humanPlayer.bet) {
      return;
    }

    await SoundManager.playChipPlace();
    
    // 扣除额外下注
    final additionalBet = humanPlayer.bet;
    final newBalance = balance - additionalBet;
    
    final updatedPlayer = humanPlayer.copyWith(bet: humanPlayer.bet * 2);
    updatedPlayer.split();

    _updateGameState(
      balance: newBalance,
      humanPlayer: updatedPlayer,
    );
    
    await _saveBalance();
    notifyListeners();
    
    // 为每个分牌手牌发一张牌
    await _dealSplitCards();
  }

  // 为分牌发牌
  Future<void> _dealSplitCards() async {
    final List<PlayingCard> newDeck = List.from(_gameState.deck);
    
    // 为第一个手牌发牌
    final card1 = DeckService.dealCard(newDeck);
    if (card1 != null) {
      humanPlayer.addCard(card1);
      await SoundManager.playCardDeal();
      await Future.delayed(const Duration(milliseconds: 300));
    }
    
    // 为第二个手牌发牌
    final card2 = DeckService.dealCard(newDeck);
    if (card2 != null) {
      humanPlayer.moveToNextSplitHand();
      humanPlayer.addCard(card2);
      humanPlayer.currentHandIndex = 0; // 回到第一个手牌
      await SoundManager.playCardDeal();
    }
    
    _updateGameState(
      deck: newDeck,
      statusMessage: 'Playing split hand 1 - Hit or Stand?',
    );
    notifyListeners();
  }

  // 投降
  Future<void> surrender() async {
    if (phase != GamePhase.playing || !humanPlayer.canSurrender) {
      return;
    }

    final surrenderAmount = (humanPlayer.bet * 0.5).round();
    final newBalance = balance + surrenderAmount;
    
    final updatedPlayer = humanPlayer.copyWith(hasSurrendered: true);
    
    _updateGameState(
      balance: newBalance,
      humanPlayer: updatedPlayer,
      phase: GamePhase.finished,
      statusMessage: 'Surrendered - Half bet returned',
    );
    
    await _saveBalance();
    notifyListeners();
    
    // 延迟后重置游戏
    await Future.delayed(const Duration(seconds: 2));
    await _resetForNextRound();
  }

  // 更新游戏状态的辅助方法
  void _updateGameState({
    List<PlayingCard>? deck,
    List<PlayingCard>? discardPile,
    Player? dealer,
    Player? humanPlayer,
    GamePhase? phase,
    GameSettings? settings,
    int? balance,
    bool? insuranceOffered,
    String? statusMessage,
    bool? isShuffling,
  }) {
    _gameState = _gameState.copyWith(
      deck: deck,
      discardPile: discardPile,
      dealer: dealer,
      humanPlayer: humanPlayer,
      phase: phase,
      settings: settings,
      balance: balance,
      insuranceOffered: insuranceOffered,
      statusMessage: statusMessage,
      isShuffling: isShuffling,
    );
  }

  // 保存余额
  Future<void> _saveBalance() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt('player_balance', balance);
    } catch (e) {
      print('Failed to save balance: $e');
    }
  }

  // 加载余额
  Future<void> _loadBalance() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedBalance = prefs.getInt('player_balance') ?? 1000;
      _updateGameState(balance: savedBalance);
    } catch (e) {
      print('Failed to load balance: $e');
    }
  }

  // 洗牌
  Future<void> _shuffleDeck() async {
    _updateGameState(
      isShuffling: true,
      statusMessage: 'Shuffling deck...',
    );
    notifyListeners();

    await SoundManager.playShuffle();
    await Future.delayed(const Duration(seconds: 2));

    final reshuffled = DeckService.reshuffleWithDiscardPile(
      _gameState.deck,
      _gameState.discardPile,
    );

    _updateGameState(
      deck: reshuffled['deck']!,
      discardPile: reshuffled['discardPile']!,
      isShuffling: false,
    );
    notifyListeners();
  }

  // 翻开庄家隐藏牌
  Future<void> _revealDealerHiddenCard() async {
    if (dealer.cards.length >= 2 && dealer.cards[1].isHidden) {
      await SoundManager.playCardFlip();

      final revealedCard = dealer.cards[1].copyWithHidden(false);
      final updatedCards = [...dealer.cards];
      updatedCards[1] = revealedCard;

      final updatedDealer = dealer.copyWith(cards: updatedCards);
      _updateGameState(dealer: updatedDealer);
      notifyListeners();

      await Future.delayed(const Duration(milliseconds: 500));
    }
  }

  // 开始庄家回合
  Future<void> _startDealerTurn() async {
    _updateGameState(
      phase: GamePhase.dealer,
      statusMessage: 'Dealer playing...',
    );
    notifyListeners();

    await _revealDealerHiddenCard();
    await _dealerPlay();
  }

  // 庄家游戏逻辑
  Future<void> _dealerPlay() async {
    final List<PlayingCard> newDeck = List.from(_gameState.deck);
    Player currentDealer = dealer;

    while (_shouldDealerHit(currentDealer)) {
      await Future.delayed(const Duration(milliseconds: 1000));

      final newCard = DeckService.dealCard(newDeck);
      if (newCard != null) {
        await SoundManager.playCardDeal();

        currentDealer = currentDealer.copyWith(
          cards: [...currentDealer.cards, newCard],
        );

        _updateGameState(
          deck: newDeck,
          dealer: currentDealer,
        );
        notifyListeners();
      }
    }

    await Future.delayed(const Duration(milliseconds: 1000));
    await _calculateResults();
  }

  // 判断庄家是否应该要牌
  bool _shouldDealerHit(Player dealer) {
    final score = dealer.currentScore;

    if (score < 17) return true;
    if (score > 17) return false;

    // 17点时的特殊规则
    if (_gameState.settings.dealerHitsSoft17 && dealer.isSoft) {
      return true;
    }

    return false;
  }

  // 计算游戏结果
  Future<void> _calculateResults() async {
    final dealerScore = dealer.currentScore;
    final playerScore = humanPlayer.currentScore;
    final dealerBust = dealer.isBust;
    final playerBust = humanPlayer.isBust;

    int winnings = 0;
    String message = '';

    if (playerBust) {
      message = 'Player busts - Dealer wins!';
      await SoundManager.playLose();
    } else if (dealerBust) {
      winnings = humanPlayer.bet * 2;
      message = 'Dealer busts - You win!';
      await SoundManager.playWin();
    } else if (playerScore > dealerScore) {
      winnings = humanPlayer.bet * 2;
      message = 'You win!';
      await SoundManager.playWin();
    } else if (playerScore < dealerScore) {
      message = 'Dealer wins!';
      await SoundManager.playLose();
    } else {
      winnings = humanPlayer.bet; // 返还下注
      message = 'Push - It\'s a tie!';
    }

    final newBalance = balance + winnings;

    _updateGameState(
      balance: newBalance,
      phase: GamePhase.finished,
      statusMessage: message,
    );

    await _saveBalance();
    notifyListeners();

    // 延迟后重置游戏
    await Future.delayed(const Duration(seconds: 3));
    await _resetForNextRound();
  }

  // 处理玩家爆牌
  Future<void> _endGameWithPlayerBust() async {
    await SoundManager.playLose();

    _updateGameState(
      phase: GamePhase.finished,
      statusMessage: 'Bust! You lose.',
    );
    notifyListeners();

    await Future.delayed(const Duration(seconds: 2));
    await _resetForNextRound();
  }

  // 处理平局
  Future<void> _endGameWithPush() async {
    final newBalance = balance + humanPlayer.bet;

    _updateGameState(
      balance: newBalance,
      phase: GamePhase.finished,
      statusMessage: 'Both have Blackjack - Push!',
    );

    await _saveBalance();
    notifyListeners();

    await Future.delayed(const Duration(seconds: 2));
    await _resetForNextRound();
  }

  // 处理玩家21点
  Future<void> _endGameWithPlayerBlackjack() async {
    final blackjackPayout = (_gameState.settings.blackjackPayout * humanPlayer.bet).round();
    final winnings = humanPlayer.bet + blackjackPayout;
    final newBalance = balance + winnings;

    await SoundManager.playWin();

    _updateGameState(
      balance: newBalance,
      phase: GamePhase.finished,
      statusMessage: 'Blackjack! You win!',
    );

    await _saveBalance();
    notifyListeners();

    await Future.delayed(const Duration(seconds: 2));
    await _resetForNextRound();
  }

  // 处理庄家21点
  Future<void> _endGameWithDealerBlackjack() async {
    await SoundManager.playLose();

    _updateGameState(
      phase: GamePhase.finished,
      statusMessage: 'Dealer has Blackjack - You lose!',
    );
    notifyListeners();

    await Future.delayed(const Duration(seconds: 2));
    await _resetForNextRound();
  }

  // 重置下一轮
  Future<void> _resetForNextRound() async {
    // 将使用过的牌放入弃牌堆
    final usedCards = [...humanPlayer.cards, ...dealer.cards];
    final newDiscardPile = DeckService.addToDiscardPile(_gameState.discardPile, usedCards);

    // 重置玩家状态
    final resetDealer = dealer.copyWith(cards: []);
    final resetPlayer = humanPlayer.copyWith(
      cards: [],
      bet: 0,
      hasInsurance: false,
      hasSurrendered: false,
      isActive: false,
    );

    _updateGameState(
      dealer: resetDealer,
      humanPlayer: resetPlayer,
      discardPile: newDiscardPile,
      phase: GamePhase.betting,
      insuranceOffered: false,
      statusMessage: 'Place your bet to start next round',
    );

    notifyListeners();
  }

  // 购买保险
  Future<void> buyInsurance() async {
    if (!_gameState.insuranceOffered || balance < (humanPlayer.bet / 2).round()) {
      return;
    }

    final insuranceCost = (humanPlayer.bet / 2).round();
    final newBalance = balance - insuranceCost;

    final updatedPlayer = humanPlayer.copyWith(
      hasInsurance: true,
      insuranceBet: insuranceCost,
    );

    _updateGameState(
      balance: newBalance,
      humanPlayer: updatedPlayer,
      insuranceOffered: false,
    );

    await _saveBalance();
    notifyListeners();

    // 继续游戏流程
    await _checkInsuranceAndBlackjack();
  }

  // 拒绝保险
  Future<void> declineInsurance() async {
    _updateGameState(insuranceOffered: false);
    notifyListeners();

    // 继续游戏流程
    if (humanPlayer.isBlackjack || dealer.isBlackjack) {
      await _handleBlackjackSituation();
    } else {
      _startPlayerTurn();
    }
  }

  // 重新开始游戏
  Future<void> restartGame() async {
    _gameState = GameState.initial();
    await initializeGame();
  }
}
