/* Unified Settings Modal Component for Blackjack Games */

class UnifiedSettingsModal {
    constructor(gameType, options = {}) {
        this.gameType = gameType;
        this.options = {
            hasBackgroundMusic: false,
            hasEffects: true,
            hasGameSettings: false,
            hasLanguageSwitch: true,
            hasRules: true,
            hasNavigation: true,
            backgroundMusic: false,
            soundEffects: true,
            gameSettings: {},
            ...options
        };
        this.modalId = 'unified-settings-modal';
        this.init();

        // Bind global fullscreen listeners once per instance
        UnifiedSettingsModal.ensureFullscreenListeners();
    }

    init() {
        // Load settings first to get cached preferences
        this.loadSettings();

        // Get current audio states after loading settings
        if (typeof window.isMusicEnabled === 'function') {
            this.options.backgroundMusic = window.isMusicEnabled();
        }
        if (typeof window.isEffectsEnabled === 'function') {
            this.options.soundEffects = window.isEffectsEnabled();
        }

        this.createModal();
        this.bindEvents();
    }

    createModal() {
        if (document.getElementById(this.modalId)) {
            document.getElementById(this.modalId).remove();
        }

        const modalHTML = this.generateModalHTML();
        document.body.insertAdjacentHTML('beforeend', modalHTML);
    }

    generateModalHTML() {
        const title = 'Game Settings';
        
        return `
            <div id="${this.modalId}" class="settings-modal-overlay">
                <div class="settings-modal-content">
                    <div class="settings-modal-header">
                        <h3 class="settings-modal-title">${title}</h3>
                        <button class="settings-modal-close" id="settings-modal-close">&times;</button>
                    </div>
                    <div class="settings-modal-body">
                        ${this.generateNavigationSection()}
                        ${this.generateAudioSection()}
                        ${this.generateGameSettingsSection()}
                        ${this.generateLanguageSection()}
                        ${this.generateRulesSection()}
                        ${this.generateShareSection()}
                        ${this.generateFeedbackSection()}
                    </div>
                </div>
            </div>
        `;
    }

    generateNavigationSection() {
        if (!this.options.hasNavigation) {
            return '';
        }

        const homeLabel = window.i18n ? window.i18n.t('settings.home') : 'Home';
        const fullscreenLabel = window.i18n ? window.i18n.t('settings.fullscreen') : 'Fullscreen';

        return `
            <div class="settings-section">
                <div class="settings-control-group">
                    <div class="settings-control-item">
                        <div class="settings-navigation-buttons">
                            <button id="nav-home-btn" class="settings-rules-link">
                                <span>🏠</span>
                                <span>${homeLabel}</span>
                            </button>
                            <button id="nav-fullscreen-btn" class="settings-rules-link">
                                <span id="nav-fullscreen-icon">⛶</span>
                                <span id="nav-fullscreen-text">${fullscreenLabel}</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    generateAudioSection() {
        if (!this.options.hasBackgroundMusic && !this.options.hasEffects) {
            return '';
        }

        const audioTitle = window.i18n ? window.i18n.t('settings.audioTitle') : '🎵 Audio Settings';
        let content = `
            <div class="settings-section">
                <div class="settings-section-title">${audioTitle}</div>
                <div class="settings-control-group">
        `;

        if (this.options.hasBackgroundMusic) {
            const musicLabel = window.i18n ? window.i18n.t('settings.backgroundMusic') : 'Background Music';
            content += `
                <div class="settings-control-item">
                    <label class="settings-control-label">${musicLabel}</label>
                    <div class="settings-single-button">
                        <button id="music-toggle" class="settings-toggle-button">OFF</button>
                    </div>
                </div>
            `;
        }

        if (this.options.hasEffects) {
            const effectsLabel = window.i18n ? window.i18n.t('settings.soundEffects') : 'Sound Effects';
            content += `
                <div class="settings-control-item">
                    <label class="settings-control-label">${effectsLabel}</label>
                    <div class="settings-single-button">
                        <button id="effects-toggle" class="settings-toggle-button">OFF</button>
                    </div>
                </div>
            `;
        }

        content += `
                </div>
            </div>
        `;

        return content;
    }

    generateGameSettingsSection() {
        if (!this.options.hasGameSettings) {
            return '';
        }

        const gameSettingsTitle = window.i18n ? window.i18n.t('settings.title') : '🎮 Game Settings';
        const deckCountLabel = window.i18n ? window.i18n.t('settings.numberOfDecks') : 'Number of Decks';

        // Get current deck count from game or options
        let currentDeckCount = 6; // default
        if (this.options.gameSettings && this.options.gameSettings.deckCount) {
            currentDeckCount = this.options.gameSettings.deckCount;
        } else if (typeof window.getCurrentDeckCount === 'function') {
            currentDeckCount = window.getCurrentDeckCount();
        } else if (window.gameSettings && window.gameSettings.deckCount) {
            currentDeckCount = window.gameSettings.deckCount;
        }

        // Generate deck options based on game type
        let deckOptions = [];
        if (this.gameType === 'blackjack-online') {
            deckOptions = [1, 6];
        } else if (this.gameType === 'free-bet-blackjack') {
            deckOptions = [6, 8];
        } else if (this.gameType === 'pontoon-card-game') {
            deckOptions = [2, 6, 8];
        } else {
            deckOptions = [1, 2, 4, 6, 8];
        }

        const deckButtons = deckOptions.map(count => {
            const isActive = count === currentDeckCount ? 'active' : '';
            return `<button class="settings-option-button deck-option-btn ${isActive}" data-decks="${count}">${count}</button>`;
        }).join('');

        return `
            <div class="settings-section">
                <div class="settings-section-title">${gameSettingsTitle}</div>
                <div class="settings-game-options">
                    <div class="settings-option-row">
                        <span class="settings-control-label">${deckCountLabel}</span>
                        <div class="settings-option-buttons">
                            ${deckButtons}
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    generateLanguageSection() {
        if (!this.options.hasLanguageSwitch) {
            return '';
        }

        const languageTitle = '🌐 Language';
        
        return `
            <div class="settings-section">
                <div class="settings-section-title">${languageTitle}</div>
                <div class="settings-language-switcher">
                    <button class="settings-language-button language-switch-btn active" data-lang="en">English</button>
                    <button class="settings-language-button language-switch-btn" data-lang="de">Deutsch</button>
                </div>
            </div>
        `;
    }

    generateRulesSection() {
        if (!this.options.hasRules) {
            return '';
        }

        const viewRulesLabel = window.i18n ? window.i18n.t('settings.rules') : 'Rules';

        return `
            <div class="settings-section">
                <div class="settings-single-button">
                    <button id="view-rules-btn" class="settings-rules-link">
                        <span>❓</span>
                        <span>${viewRulesLabel}</span>
                    </button>
                </div>
            </div>
        `;
    }

    getImageBasePath() {
        // Determine the current game path based on URL
        const path = window.location.pathname;
        if (path.includes('/blackjack-online/')) {
            return '/blackjack-online/images';
        } else if (path.includes('/free-bet-blackjack/')) {
            return '/free-bet-blackjack/images';
        } else if (path.includes('/pontoon-card-game/')) {
            return '/pontoon-card-game/images';
        } else {
            return '/blackjack-simulator/images';
        }
    }

    generateShareSection() {
        if (!this.options.hasShare) {
            return '';
        }

        const shareText = window.i18n ? window.i18n.t('settings.shareText') : 'If you like our game, recommend it to more friends!';
        const basePath = this.getImageBasePath();

        return `
            <div class="settings-section share-section">
                <div class="share-text">
                    <p>${shareText}</p>
                </div>
                <div class="share-buttons">
                    <button id="share-facebook" class="share-button facebook" title="Share on Facebook">
                        <img src="${basePath}/facebook.svg" alt="Facebook" />
                    </button>
                    <button id="share-twitter" class="share-button twitter" title="Share on Twitter">
                        <img src="${basePath}/twitter.svg" alt="Twitter" />
                    </button>
                    <button id="share-whatsapp" class="share-button whatsapp" title="Share on WhatsApp">
                        <img src="${basePath}/whatsapp.svg" alt="WhatsApp" />
                    </button>
                    <button id="share-telegram" class="share-button telegram" title="Share on Telegram">
                        <img src="${basePath}/telegram.svg" alt="Telegram" />
                    </button>
                    <button id="share-copy" class="share-button copy" title="Copy Link">
                        <img src="${basePath}/link.svg" alt="Copy Link" />
                    </button>
                </div>
            </div>
        `;
    }

    generateFeedbackSection() {
        const feedbackText = window.i18n ? window.i18n.t('settings.feedbackText') : 'Have suggestions or found an issue? We\'d love to hear from you!';
        const feedbackButtonText = window.i18n ? window.i18n.t('settings.feedbackButton') : 'Send Feedback';
        const feedbackButtonTitle = window.i18n ? window.i18n.t('settings.feedbackButtonTitle') : 'Send Feedback';

        return `
            <div class="settings-section feedback-section">
                <div class="feedback-text">
                    <p>${feedbackText}</p>
                </div>
                <div class="feedback-button-container">
                    <button id="feedback-btn" class="feedback-button" title="${feedbackButtonTitle}">
                        <span>💬</span>
                        <span>${feedbackButtonText}</span>
                    </button>
                </div>
            </div>
        `;
    }

    bindEvents() {
        const modal = document.getElementById(this.modalId);
        
        // Close modal events
        document.getElementById('settings-modal-close').addEventListener('click', () => {
            this.hide();
        });
        
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                this.hide();
            }
        });

        // Navigation controls
        if (this.options.hasNavigation) {
            this.bindNavigationControls();
        }

        // Audio controls
        if (this.options.hasBackgroundMusic) {
            this.bindMusicControls();
        }

        if (this.options.hasEffects) {
            this.bindEffectsControls();
        }

        // Game settings
        if (this.options.hasGameSettings) {
            this.bindGameSettingsControls();
        }

        // Language switcher
        if (this.options.hasLanguageSwitch) {
            this.bindLanguageControls();
        }

        // Rules
        if (this.options.hasRules) {
            this.bindRulesControls();
        }

        // Share buttons
        this.bindShareControls();

        // Feedback button
        this.bindFeedbackControls();

        // Sync fullscreen button state initially
        this.updateFullscreenButton();
    }

    bindNavigationControls() {
        const homeButton = document.getElementById('nav-home-btn');
        const fullscreenButton = document.getElementById('nav-fullscreen-btn');

        homeButton.addEventListener('click', () => {
            window.location.href = '/';
        });

        fullscreenButton.addEventListener('click', () => {
            if (typeof window.toggleFullscreen === 'function') {
                window.toggleFullscreen();
            } else {
                this.toggleFullscreen();
            }
            // Close modal immediately after triggering fullscreen
            this.hide();
            // Defer UI sync slightly to allow fullscreenchange to propagate
            setTimeout(() => this.updateFullscreenButton(), 100);
        });

        this.updateFullscreenButton();
    }

    // Helper to check if page is effectively in fullscreen (also heuristics for F11)
    static isFullscreenLike() {
        const std = !!document.fullscreenElement;
        const vendor = !!(document.webkitFullscreenElement || document.mozFullScreenElement || document.msFullscreenElement);
        const inStandalone = (window.matchMedia && (window.matchMedia('(display-mode: fullscreen)').matches || window.matchMedia('(display-mode: standalone)').matches)) || false;
        // Desktop-only F11 heuristic: ignore on touch/mobile to avoid false positives in landscape with hidden browser UI
        const isTouch = ('ontouchstart' in window) || (navigator.maxTouchPoints && navigator.maxTouchPoints > 0);
        const f11LikeDesktop = !isTouch && (Math.abs(window.innerHeight - screen.height) < 2 || Math.abs(window.innerWidth - screen.width) < 2);
        return std || vendor || inStandalone || f11LikeDesktop;
    }

    static ensureFullscreenListeners() {
        if (window.__unifiedFullscreenListenersBound) return;
        window.__unifiedFullscreenListenersBound = true;

        const syncUI = () => {
            if (window.unifiedSettingsModal) {
                window.unifiedSettingsModal.updateFullscreenButton();
            }
            // Show/hide non-game sections on fullscreen changes
            const isFs = UnifiedSettingsModal.isFullscreenLike();
            const toggleDisplay = (selector, show) => {
                document.querySelectorAll(selector).forEach(el => {
                    if (show) {
                        el.style.display = '';
                    } else {
                        el.style.display = 'none';
                    }
                });
            };
            toggleDisplay('.seo-content-section', !isFs);
            toggleDisplay('.game-recommendations.similar-games', !isFs);
            toggleDisplay('.game-recommendations.other-games', !isFs);
        };

        // Listen to standard and vendor-prefixed fullscreen events
        ['fullscreenchange','webkitfullscreenchange','mozfullscreenchange','MSFullscreenChange'].forEach(evt => {
            document.addEventListener(evt, syncUI);
        });

        // Also listen to resize and orientation changes to detect F11 and mobile changes
        window.addEventListener('resize', () => setTimeout(syncUI, 50));
        if (window.screen && screen.orientation && screen.orientation.addEventListener) {
            screen.orientation.addEventListener('change', () => setTimeout(syncUI, 50));
        }

        // Initial sync
        setTimeout(syncUI, 0);
    }

    toggleFullscreen() {
        if (!document.fullscreenElement) {
            document.documentElement.requestFullscreen().catch(() => {});
        } else {
            if (document.exitFullscreen) {
                document.exitFullscreen().catch(() => {});
            }
        }
    }

    updateFullscreenButton() {
        const icon = document.getElementById('nav-fullscreen-icon');
        const text = document.getElementById('nav-fullscreen-text');
        if (icon && text) {
            const isFullscreen = UnifiedSettingsModal.isFullscreenLike();
            icon.textContent = '⛶';
            text.textContent = isFullscreen ? 'Exit Fullscreen' : 'Fullscreen';
        }
    }

    bindMusicControls() {
        const musicToggle = document.getElementById('music-toggle');

        musicToggle.addEventListener('click', () => {
            if (typeof window.toggleMusic === 'function') {
                window.toggleMusic();
            }

            // Update current state
            if (typeof window.isMusicEnabled === 'function') {
                this.options.backgroundMusic = window.isMusicEnabled();
            } else {
                this.options.backgroundMusic = !this.options.backgroundMusic;
            }

            this.updateMusicToggleButton();
            this.saveSettings();
        });
    }

    bindEffectsControls() {
        const effectsToggle = document.getElementById('effects-toggle');

        effectsToggle.addEventListener('click', () => {
            if (typeof window.toggleEffects === 'function') {
                window.toggleEffects();
            }

            // Update current state
            if (typeof window.isEffectsEnabled === 'function') {
                this.options.soundEffects = window.isEffectsEnabled();
            } else {
                this.options.soundEffects = !this.options.soundEffects;
            }

            this.updateEffectsToggleButton();
            this.saveSettings();
        });
    }

    bindGameSettingsControls() {
        const deckButtons = document.querySelectorAll('.deck-option-btn');
        deckButtons.forEach(button => {
            button.addEventListener('click', () => {
                deckButtons.forEach(btn => btn.classList.remove('active'));
                button.classList.add('active');

                const deckCount = parseInt(button.dataset.decks);
                if (typeof window.setDeckCount === 'function') {
                    window.setDeckCount(deckCount);
                }

                // Update options and save settings
                if (!this.options.gameSettings) {
                    this.options.gameSettings = {};
                }
                this.options.gameSettings.deckCount = deckCount;
                this.saveSettings();
            });
        });

        // Player count controls
        const playerButtons = document.querySelectorAll('.player-option-btn');
        playerButtons.forEach(button => {
            button.addEventListener('click', () => {
                playerButtons.forEach(btn => btn.classList.remove('active'));
                button.classList.add('active');

                const playerCount = parseInt(button.dataset.players);
                if (typeof window.setPlayerCount === 'function') {
                    window.setPlayerCount(playerCount);
                }

                // Update options and save settings
                if (!this.options.gameSettings) {
                    this.options.gameSettings = {};
                }
                this.options.gameSettings.playerCount = playerCount;
                this.saveSettings();
            });
        });
    }

    bindLanguageControls() {
        const languageButtons = document.querySelectorAll('.language-switch-btn');
        languageButtons.forEach(button => {
            button.addEventListener('click', async () => {
                const lang = button.dataset.lang;
                if (window.i18n && typeof window.i18n.changeLanguage === 'function') {
                    // 只有当点击的不是当前语言时才切换
                    if (window.i18n.currentLanguage !== lang) {
                        await window.i18n.changeLanguage(lang);
                        languageButtons.forEach(btn => btn.classList.remove('active'));
                        button.classList.add('active');
                        // 重新生成弹窗内容以更新语言
                        this.updateModalContent();
                        // 保存语言设置
                        this.saveSettings();
                    }
                }
            });
        });
    }

    bindRulesControls() {
        const rulesButton = document.getElementById('view-rules-btn');
        rulesButton.addEventListener('click', () => {
            this.hide();
            // 添加延迟确保设置弹窗完全关闭后再打开规则弹窗
            setTimeout(() => {
                if (typeof window.showRulesModal === 'function') {
                    window.showRulesModal();
                }
            }, 100);
        });
    }

    show() {
        const modal = document.getElementById(this.modalId);
        if (modal) {
            // Update current states before showing
            if (typeof window.isMusicEnabled === 'function') {
                this.options.backgroundMusic = window.isMusicEnabled();
            }
            if (typeof window.isEffectsEnabled === 'function') {
                this.options.soundEffects = window.isEffectsEnabled();
            }

            modal.style.display = 'flex';
            document.body.style.overflow = 'hidden';
            this.updateUI();
        }
    }

    hide() {
        const modal = document.getElementById(this.modalId);
        if (modal) {
            modal.style.display = 'none';
            document.body.style.overflow = 'auto';
        }
    }

    updateUI() {
        if (this.options.hasBackgroundMusic) {
            this.updateMusicToggleButton();
        }

        if (this.options.hasEffects) {
            this.updateEffectsToggleButton();
        }

        if (this.options.hasLanguageSwitch) {
            this.updateLanguageButtons();
        }
    }

    updateMusicToggleButton() {
        const button = document.getElementById('music-toggle');
        if (button && typeof window.isMusicMuted === 'function') {
            const isMuted = window.isMusicMuted();
            button.textContent = isMuted ? 'OFF' : 'ON';
            button.classList.toggle('active', !isMuted);
        }
    }

    updateEffectsToggleButton() {
        const button = document.getElementById('effects-toggle');
        if (button && typeof window.isEffectsMuted === 'function') {
            const isMuted = window.isEffectsMuted();
            button.textContent = isMuted ? 'OFF' : 'ON';
            button.classList.toggle('active', !isMuted);
        }
    }



    updateLanguageButtons() {
        const buttons = document.querySelectorAll('.language-switch-btn');
        if (window.i18n && window.i18n.currentLanguage) {
            buttons.forEach(button => {
                button.classList.toggle('active', button.dataset.lang === window.i18n.currentLanguage);
            });
        }
    }

    bindShareControls() {
        const shareButtons = {
            facebook: document.getElementById('share-facebook'),
            twitter: document.getElementById('share-twitter'),
            whatsapp: document.getElementById('share-whatsapp'),
            telegram: document.getElementById('share-telegram'),
            copy: document.getElementById('share-copy')
        };

        const currentUrl = window.location.href;
        const gameTitle = document.title || 'Blackjack Game';
        const shareText = window.i18n ?
            window.i18n.t('share.message', { title: gameTitle }) :
            `Check out this amazing ${gameTitle}! Play for free and test your skills.`;

        if (shareButtons.facebook) {
            shareButtons.facebook.addEventListener('click', () => {
                const facebookUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(currentUrl)}`;
                window.open(facebookUrl, '_blank', 'width=600,height=400');
            });
        }

        if (shareButtons.twitter) {
            shareButtons.twitter.addEventListener('click', () => {
                const twitterUrl = `https://twitter.com/intent/tweet?url=${encodeURIComponent(currentUrl)}&text=${encodeURIComponent(shareText)}`;
                window.open(twitterUrl, '_blank', 'width=600,height=400');
            });
        }

        if (shareButtons.whatsapp) {
            shareButtons.whatsapp.addEventListener('click', () => {
                const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(shareText + ' ' + currentUrl)}`;
                window.open(whatsappUrl, '_blank');
            });
        }

        if (shareButtons.telegram) {
            shareButtons.telegram.addEventListener('click', () => {
                const telegramUrl = `https://t.me/share/url?url=${encodeURIComponent(currentUrl)}&text=${encodeURIComponent(shareText)}`;
                window.open(telegramUrl, '_blank');
            });
        }

        if (shareButtons.copy) {
            shareButtons.copy.addEventListener('click', async () => {
                try {
                    await navigator.clipboard.writeText(currentUrl);
                    this.showCopySuccessMessage();
                    shareButtons.copy.classList.add('copied');
                    shareButtons.copy.title = 'Copied!';

                    setTimeout(() => {
                        shareButtons.copy.classList.remove('copied');
                        shareButtons.copy.title = 'Copy Link';
                    }, 2000);
                } catch (err) {
                    // Fallback for older browsers
                    const textArea = document.createElement('textarea');
                    textArea.value = currentUrl;
                    document.body.appendChild(textArea);
                    textArea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textArea);

                    this.showCopySuccessMessage();
                    shareButtons.copy.classList.add('copied');
                    shareButtons.copy.title = 'Copied!';

                    setTimeout(() => {
                        shareButtons.copy.classList.remove('copied');
                        shareButtons.copy.title = 'Copy Link';
                    }, 2000);
                }
            });
        }
    }

    showCopySuccessMessage() {
        // Remove any existing copy message
        const existingMessage = document.querySelector('.copy-success-message');
        if (existingMessage) {
            existingMessage.remove();
        }

        // Create success message
        const message = document.createElement('div');
        message.className = 'copy-success-message';
        message.textContent = window.i18n ? window.i18n.t('share.copied') : 'Link copied to clipboard!';

        // Position the message
        const modal = document.querySelector('.settings-modal');
        if (modal) {
            modal.appendChild(message);

            // Trigger animation
            setTimeout(() => {
                message.classList.add('show');
            }, 10);

            // Remove message after 3 seconds
            setTimeout(() => {
                message.classList.remove('show');
                setTimeout(() => {
                    if (message.parentNode) {
                        message.parentNode.removeChild(message);
                    }
                }, 300);
            }, 3000);
        }
    }

    bindFeedbackControls() {
        const feedbackButton = document.getElementById('feedback-btn');
        if (feedbackButton) {
            feedbackButton.addEventListener('click', () => {
                // Open feedback page in new tab
                // Try clean URL first, fallback to .html if needed
                window.open('/feedback', '_blank');
            });
        }
    }

    updateModalContent() {
        const modalBody = document.querySelector('.settings-modal-body');
        if (modalBody) {
            // Update current audio states before regenerating content
            if (typeof window.isMusicEnabled === 'function') {
                this.options.backgroundMusic = window.isMusicEnabled();
            }
            if (typeof window.isEffectsEnabled === 'function') {
                this.options.soundEffects = window.isEffectsEnabled();
            }

            modalBody.innerHTML = `
                ${this.generateNavigationSection()}
                ${this.generateAudioSection()}
                ${this.generateGameSettingsSection()}
                ${this.generateLanguageSection()}
                ${this.generateRulesSection()}
                ${this.generateShareSection()}
                ${this.generateFeedbackSection()}
            `;
            this.bindEvents();
            this.updateUI();
        }
    }

    loadSettings() {
        const cacheKey = `settings_${this.gameType}`;
        const savedSettings = localStorage.getItem(cacheKey);

        if (savedSettings) {
            try {
                const settings = JSON.parse(savedSettings);

                // Apply audio settings
                if (settings.backgroundMusic !== undefined && this.options.hasBackgroundMusic) {
                    this.options.backgroundMusic = settings.backgroundMusic;
                    // Apply the setting if the global function exists
                    if (typeof window.setMusicEnabled === 'function') {
                        window.setMusicEnabled(settings.backgroundMusic);
                    } else if (typeof window.toggleMusic === 'function') {
                        // Get current state and toggle if needed
                        const currentState = window.isMusicEnabled ? window.isMusicEnabled() : false;
                        if (currentState !== settings.backgroundMusic) {
                            window.toggleMusic();
                        }
                    }
                }

                if (settings.soundEffects !== undefined) {
                    this.options.soundEffects = settings.soundEffects;
                    // Apply the setting if the global function exists
                    if (typeof window.setEffectsEnabled === 'function') {
                        window.setEffectsEnabled(settings.soundEffects);
                    } else if (typeof window.toggleEffects === 'function') {
                        // Get current state and toggle if needed
                        const currentState = window.isEffectsEnabled ? window.isEffectsEnabled() : false;
                        if (currentState !== settings.soundEffects) {
                            window.toggleEffects();
                        }
                    }
                }

                // Apply game settings
                if (settings.gameSettings && this.options.hasGameSettings) {
                    Object.assign(this.options.gameSettings, settings.gameSettings);

                    // Apply specific game settings
                    if (settings.gameSettings.playerCount && typeof window.setPlayerCount === 'function') {
                        window.setPlayerCount(settings.gameSettings.playerCount);
                    }

                    if (settings.gameSettings.deckCount && typeof window.setDeckCount === 'function') {
                        window.setDeckCount(settings.gameSettings.deckCount);
                    }
                }

                // Apply language setting
                if (settings.language && window.i18n && typeof window.i18n.changeLanguage === 'function') {
                    if (window.i18n.currentLanguage !== settings.language) {
                        window.i18n.changeLanguage(settings.language);
                    }
                }

            } catch (error) {
                console.warn('Failed to load settings from cache:', error);
            }
        }
    }

    saveSettings() {
        const cacheKey = `settings_${this.gameType}`;

        // Get current audio states from global functions if available
        let backgroundMusic = this.options.backgroundMusic;
        let soundEffects = this.options.soundEffects;

        if (typeof window.isMusicEnabled === 'function') {
            backgroundMusic = window.isMusicEnabled();
        }

        if (typeof window.isEffectsEnabled === 'function') {
            soundEffects = window.isEffectsEnabled();
        }

        const settings = {
            backgroundMusic: backgroundMusic,
            soundEffects: soundEffects,
            gameSettings: this.options.gameSettings || {},
            language: window.i18n ? window.i18n.currentLanguage : 'en',
            timestamp: Date.now()
        };

        try {
            localStorage.setItem(cacheKey, JSON.stringify(settings));
        } catch (error) {
            console.warn('Failed to save settings to cache:', error);
        }
    }
}

// Global function to create and show settings modal
window.showUnifiedSettingsModal = function(gameType, options) {
    if (!window.unifiedSettingsModal || window.unifiedSettingsModal.gameType !== gameType) {
        window.unifiedSettingsModal = new UnifiedSettingsModal(gameType, options);
    } else {
        // Update options if modal already exists for the same game type
        Object.assign(window.unifiedSettingsModal.options, options);
        // Reload settings to ensure they are current
        window.unifiedSettingsModal.loadSettings();
    }
    window.unifiedSettingsModal.show();
};

// Auto-apply settings on page load for each game
window.autoApplyGameSettings = function(gameType) {
    const cacheKey = `settings_${gameType}`;
    const savedSettings = localStorage.getItem(cacheKey);

    if (savedSettings) {
        try {
            const settings = JSON.parse(savedSettings);

            // Apply deck count setting
            if (settings.gameSettings && settings.gameSettings.deckCount && typeof window.setDeckCount === 'function') {
                window.setDeckCount(settings.gameSettings.deckCount);
            }

            // Apply language setting
            if (settings.language && window.i18n && typeof window.i18n.changeLanguage === 'function') {
                if (window.i18n.currentLanguage !== settings.language) {
                    window.i18n.changeLanguage(settings.language);
                }
            }

        } catch (error) {
            console.warn('Failed to auto-apply settings:', error);
        }
    }
};

// Ensure fullscreen listeners are active regardless of whether the settings modal has been opened
try {
    if (typeof UnifiedSettingsModal !== 'undefined' && UnifiedSettingsModal.ensureFullscreenListeners) {
        UnifiedSettingsModal.ensureFullscreenListeners();
    }
} catch (_) {}
