#!/usr/bin/env node

// Enhanced Game Management System
// Advanced tools for managing games, categories, and automated updates

const fs = require('fs');
const path = require('path');
const readline = require('readline');

// Import configuration and existing tools
const { 
    systemConfig, 
    gameCategories, 
    completeGameData, 
    addNewGame, 
    updateGame, 
    removeGame, 
    addNewCategory, 
    getGameStatistics 
} = require('./game-config.js');

const { 
    updateGamePageRecommendations, 
    updateHomepageCategories, 
    updateSitemap, 
    validateAllLayouts 
} = require('./update-all-games.js');

// Configuration
const BASE_DIR = path.dirname(__dirname);
const BACKUP_DIR = path.join(BASE_DIR, 'backups');

// Interactive CLI interface
const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

// Utility functions
function createTimestampedBackup(filePath, description = '') {
    if (fs.existsSync(filePath)) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const backupName = `${path.basename(filePath)}.${timestamp}${description ? '.' + description : ''}.backup`;
        const backupPath = path.join(BACKUP_DIR, backupName);
        fs.copyFileSync(filePath, backupPath);
        console.log(`✅ Backup created: ${backupName}`);
        return backupPath;
    }
    return null;
}

function saveConfigChanges() {
    const configPath = path.join(__dirname, 'game-config.js');
    createTimestampedBackup(configPath, 'config-update');
    
    // Note: In a real implementation, you'd need to serialize the config back to the file
    // This is a simplified version for demonstration
    console.log('📝 Configuration changes saved');
}

// Enhanced game management functions
async function addGameInteractive() {
    console.log('\n🎮 Adding New Game\n');
    
    const gameKey = await askQuestion('Game key (e.g., "new-puzzle-game"): ');
    const name = await askQuestion('Game name: ');
    const icon = await askQuestion('Game icon (emoji): ');
    const description = await askQuestion('Game description: ');
    const url = await askQuestion('Game URL (e.g., "/new-puzzle-game"): ');
    
    console.log('\nAvailable categories:');
    Object.keys(gameCategories).forEach(key => {
        console.log(`  ${key}: ${gameCategories[key].name}`);
    });
    
    const category = await askQuestion('Category: ');
    const rating = await askQuestion('Rating (default 4.5): ') || '4.5';
    const difficulty = await askQuestion('Difficulty (⭐⭐⭐): ') || '⭐⭐⭐';
    const tags = (await askQuestion('Tags (comma-separated): ')).split(',').map(tag => tag.trim());
    
    const gameData = {
        name,
        icon,
        description,
        url,
        category,
        rating,
        difficulty,
        tags: tags.filter(tag => tag.length > 0)
    };
    
    if (addNewGame(gameKey, gameData)) {
        console.log(`✅ Game "${name}" added successfully!`);
        saveConfigChanges();
        
        const updateAll = await askQuestion('Update all pages now? (y/n): ');
        if (updateAll.toLowerCase() === 'y') {
            await updateAllPages();
        }
    } else {
        console.log('❌ Failed to add game');
    }
}

async function updateGameInteractive() {
    console.log('\n📝 Updating Existing Game\n');
    
    console.log('Available games:');
    Object.keys(completeGameData).forEach(key => {
        console.log(`  ${key}: ${completeGameData[key].name}`);
    });
    
    const gameKey = await askQuestion('Game key to update: ');
    
    if (!completeGameData[gameKey]) {
        console.log('❌ Game not found');
        return;
    }
    
    const currentGame = completeGameData[gameKey];
    console.log(`\nCurrent game data for "${currentGame.name}":`);
    console.log(`  Name: ${currentGame.name}`);
    console.log(`  Category: ${currentGame.category}`);
    console.log(`  Rating: ${currentGame.rating}`);
    console.log(`  Description: ${currentGame.description}`);
    
    const updates = {};
    
    const newName = await askQuestion(`New name (current: ${currentGame.name}): `);
    if (newName) updates.name = newName;
    
    const newCategory = await askQuestion(`New category (current: ${currentGame.category}): `);
    if (newCategory) updates.category = newCategory;
    
    const newRating = await askQuestion(`New rating (current: ${currentGame.rating}): `);
    if (newRating) updates.rating = newRating;
    
    const newDescription = await askQuestion(`New description (current: ${currentGame.description}): `);
    if (newDescription) updates.description = newDescription;
    
    if (Object.keys(updates).length > 0) {
        if (updateGame(gameKey, updates)) {
            console.log(`✅ Game "${gameKey}" updated successfully!`);
            saveConfigChanges();
            
            const updatePages = await askQuestion('Update affected pages? (y/n): ');
            if (updatePages.toLowerCase() === 'y') {
                await updateSpecificGamePages([gameKey]);
            }
        } else {
            console.log('❌ Failed to update game');
        }
    } else {
        console.log('No changes made');
    }
}

async function showStatistics() {
    console.log('\n📊 Game Statistics\n');
    
    const stats = getGameStatistics();
    
    console.log(`Total Games: ${stats.totalGames}`);
    console.log(`Average Rating: ${stats.averageRating.toFixed(2)}`);
    
    console.log('\nGames by Category:');
    Object.entries(stats.categories).forEach(([key, data]) => {
        console.log(`  ${data.name}: ${data.count} games (avg rating: ${data.averageRating.toFixed(2)})`);
    });
    
    console.log('\nDifficulty Distribution:');
    Object.entries(stats.difficultyDistribution).forEach(([level, count]) => {
        console.log(`  ${'⭐'.repeat(level)} difficulty: ${count} games`);
    });
}

async function updateAllPages() {
    console.log('\n🔄 Updating All Pages...\n');
    
    // Update all game pages
    let successCount = 0;
    const gameKeys = Object.keys(completeGameData);
    
    for (const gameKey of gameKeys) {
        if (updateGamePageRecommendations(gameKey)) {
            successCount++;
        }
    }
    
    // Update homepage
    if (updateHomepageCategories()) {
        successCount++;
    }
    
    // Update sitemap
    if (updateSitemap()) {
        successCount++;
    }
    
    console.log(`\n✅ Updated ${successCount}/${gameKeys.length + 2} pages successfully`);
}

async function updateSpecificGamePages(gameKeys) {
    console.log(`\n🔄 Updating ${gameKeys.length} specific game pages...\n`);
    
    let successCount = 0;
    for (const gameKey of gameKeys) {
        if (updateGamePageRecommendations(gameKey)) {
            successCount++;
        }
    }
    
    console.log(`✅ Updated ${successCount}/${gameKeys.length} pages successfully`);
}

function askQuestion(question) {
    return new Promise((resolve) => {
        rl.question(question, (answer) => {
            resolve(answer.trim());
        });
    });
}

// Main menu
async function showMainMenu() {
    console.log('\n🎮 Enhanced Game Manager\n');
    console.log('1. Add new game');
    console.log('2. Update existing game');
    console.log('3. Remove game');
    console.log('4. Show statistics');
    console.log('5. Update all pages');
    console.log('6. Validate layouts');
    console.log('7. Exit');
    
    const choice = await askQuestion('\nSelect option (1-7): ');
    
    switch (choice) {
        case '1':
            await addGameInteractive();
            break;
        case '2':
            await updateGameInteractive();
            break;
        case '3':
            console.log('Remove game functionality - to be implemented');
            break;
        case '4':
            await showStatistics();
            break;
        case '5':
            await updateAllPages();
            break;
        case '6':
            validateAllLayouts();
            break;
        case '7':
            console.log('👋 Goodbye!');
            rl.close();
            return;
        default:
            console.log('Invalid option');
    }
    
    await showMainMenu();
}

// Export functions
module.exports = {
    addGameInteractive,
    updateGameInteractive,
    showStatistics,
    updateAllPages,
    updateSpecificGamePages,
    showMainMenu
};

// Run if called directly
if (require.main === module) {
    showMainMenu().catch(console.error);
}
