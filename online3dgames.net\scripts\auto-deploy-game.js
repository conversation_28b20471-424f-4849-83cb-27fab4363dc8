#!/usr/bin/env node

// Automated Game Deployment Script
// One-click solution to add new games and update all related pages

const fs = require('fs');
const path = require('path');

// Import required modules
const { 
    systemConfig, 
    addNewGame, 
    getGameStatistics 
} = require('./game-config.js');

const { 
    updateGamePageRecommendations, 
    updateHomepageCategories, 
    updateSitemap 
} = require('./update-all-games.js');

// Configuration
const BASE_DIR = path.dirname(__dirname);
const BACKUP_DIR = path.join(BASE_DIR, 'backups');

// Ensure backup directory exists
if (!fs.existsSync(BACKUP_DIR)) {
    fs.mkdirSync(BACKUP_DIR, { recursive: true });
}

// Predefined game templates for quick deployment
const gameTemplates = {
    'card-game': {
        category: 'card-games',
        difficulty: '⭐⭐⭐',
        rating: '4.5',
        tags: ['Cards', 'Strategy', 'Classic']
    },
    'puzzle-game': {
        category: 'puzzle',
        difficulty: '⭐⭐⭐',
        rating: '4.6',
        tags: ['Puzzle', 'Logic', 'Brain Training']
    },
    'arcade-game': {
        category: 'arcade',
        difficulty: '⭐⭐',
        rating: '4.4',
        tags: ['Arcade', 'Action', 'Classic']
    },
    'relaxation-game': {
        category: 'relaxation',
        difficulty: '⭐',
        rating: '4.3',
        tags: ['Relaxation', 'Stress Relief', 'Meditation']
    },
    'blackjack-variant': {
        category: 'blackjack',
        difficulty: '⭐⭐⭐⭐',
        rating: '4.7',
        tags: ['Casino', 'Strategy', 'Cards']
    }
};

// Automated deployment function
function deployNewGame(gameConfig) {
    console.log(`🚀 Starting automated deployment for: ${gameConfig.name}\n`);
    
    // Step 1: Validate configuration
    if (!validateGameConfig(gameConfig)) {
        console.error('❌ Game configuration validation failed');
        return false;
    }
    
    // Step 2: Create comprehensive backup
    createDeploymentBackup();
    
    // Step 3: Add game to configuration
    const gameKey = gameConfig.key || generateGameKey(gameConfig.name);
    
    if (!addNewGame(gameKey, gameConfig)) {
        console.error('❌ Failed to add game to configuration');
        return false;
    }
    
    console.log(`✅ Game "${gameConfig.name}" added to configuration`);
    
    // Step 4: Update all related pages
    const updateResults = performBatchUpdate(gameKey);
    
    // Step 5: Generate deployment report
    generateDeploymentReport(gameKey, gameConfig, updateResults);
    
    return updateResults.success;
}

function validateGameConfig(config) {
    const requiredFields = ['name', 'icon', 'description', 'url', 'category'];
    
    for (const field of requiredFields) {
        if (!config[field]) {
            console.error(`❌ Missing required field: ${field}`);
            return false;
        }
    }
    
    // Validate URL format
    if (!config.url.startsWith('/')) {
        console.error('❌ URL must start with "/"');
        return false;
    }
    
    // Check if game directory exists
    const gameDir = path.join(BASE_DIR, config.url.substring(1));
    if (!fs.existsSync(gameDir)) {
        console.warn(`⚠️ Game directory not found: ${gameDir}`);
        console.warn('   Make sure to create the game directory and index.html file');
    }
    
    return true;
}

function generateGameKey(name) {
    return name.toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim();
}

function createDeploymentBackup() {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupSubDir = path.join(BACKUP_DIR, `deployment-${timestamp}`);
    
    if (!fs.existsSync(backupSubDir)) {
        fs.mkdirSync(backupSubDir, { recursive: true });
    }
    
    // Backup critical files
    const criticalFiles = [
        'index.html',
        'sitemap.xml',
        'scripts/game-config.js'
    ];
    
    criticalFiles.forEach(file => {
        const sourcePath = path.join(BASE_DIR, file);
        if (fs.existsSync(sourcePath)) {
            const backupPath = path.join(backupSubDir, path.basename(file));
            fs.copyFileSync(sourcePath, backupPath);
        }
    });
    
    console.log(`📦 Deployment backup created: ${backupSubDir}`);
}

function performBatchUpdate(newGameKey) {
    console.log('\n🔄 Performing batch updates...\n');
    
    const results = {
        gamePages: 0,
        homepage: false,
        sitemap: false,
        total: 0,
        success: false
    };
    
    // Update all game pages (including the new one if it exists)
    const allGameKeys = Object.keys(require('./game-config.js').completeGameData);
    
    allGameKeys.forEach(gameKey => {
        if (updateGamePageRecommendations(gameKey)) {
            results.gamePages++;
        }
        results.total++;
    });
    
    // Update homepage
    if (updateHomepageCategories()) {
        results.homepage = true;
    }
    results.total++;
    
    // Update sitemap
    if (updateSitemap()) {
        results.sitemap = true;
    }
    results.total++;
    
    const successCount = results.gamePages + (results.homepage ? 1 : 0) + (results.sitemap ? 1 : 0);
    results.success = successCount === results.total;
    
    console.log(`\n📊 Batch update results:`);
    console.log(`   Game pages: ${results.gamePages}/${allGameKeys.length}`);
    console.log(`   Homepage: ${results.homepage ? '✅' : '❌'}`);
    console.log(`   Sitemap: ${results.sitemap ? '✅' : '❌'}`);
    console.log(`   Overall: ${successCount}/${results.total} successful`);
    
    return results;
}

function generateDeploymentReport(gameKey, gameConfig, updateResults) {
    const timestamp = new Date().toISOString();
    const stats = getGameStatistics();
    
    const report = {
        timestamp,
        gameKey,
        gameConfig,
        updateResults,
        systemStats: stats,
        status: updateResults.success ? 'SUCCESS' : 'PARTIAL_FAILURE'
    };
    
    // Save report to file
    const reportPath = path.join(BACKUP_DIR, `deployment-report-${gameKey}-${timestamp.replace(/[:.]/g, '-')}.json`);
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    
    console.log(`\n📋 Deployment Report:`);
    console.log(`   Status: ${report.status}`);
    console.log(`   Game: ${gameConfig.name} (${gameKey})`);
    console.log(`   Category: ${gameConfig.category}`);
    console.log(`   Total games in system: ${stats.totalGames}`);
    console.log(`   Report saved: ${reportPath}`);
    
    if (updateResults.success) {
        console.log('\n🎉 Deployment completed successfully!');
        console.log('\nNext steps:');
        console.log('1. Verify the game page exists and works correctly');
        console.log('2. Check that recommendations appear on other game pages');
        console.log('3. Confirm the game appears in homepage categories');
        console.log('4. Test the updated sitemap.xml');
    } else {
        console.log('\n⚠️ Deployment completed with some issues');
        console.log('Please check the logs above for specific failures');
    }
}

// Quick deployment functions using templates
function deployCardGame(name, icon, description, url) {
    const gameConfig = {
        ...gameTemplates['card-game'],
        name,
        icon,
        description,
        url,
        seoTitle: `${name} Online - Classic Card Game | Flow Fray`,
        seoDescription: `Play ${name} online for free. ${description}`
    };
    
    return deployNewGame(gameConfig);
}

function deployPuzzleGame(name, icon, description, url) {
    const gameConfig = {
        ...gameTemplates['puzzle-game'],
        name,
        icon,
        description,
        url,
        seoTitle: `${name} Online - Brain Training Puzzle Game | Flow Fray`,
        seoDescription: `Challenge your mind with ${name}. ${description}`
    };
    
    return deployNewGame(gameConfig);
}

function deployArcadeGame(name, icon, description, url) {
    const gameConfig = {
        ...gameTemplates['arcade-game'],
        name,
        icon,
        description,
        url,
        seoTitle: `${name} Online - Classic Arcade Game | Flow Fray`,
        seoDescription: `Play ${name} arcade game online. ${description}`
    };
    
    return deployNewGame(gameConfig);
}

// Command line interface
function main() {
    const args = process.argv.slice(2);
    
    if (args.length === 0) {
        console.log('🎮 Automated Game Deployment Tool\n');
        console.log('Usage examples:');
        console.log('  node auto-deploy-game.js --template card "Poker" "🃏" "Classic poker game" "/poker"');
        console.log('  node auto-deploy-game.js --template puzzle "Sudoku Pro" "🧩" "Advanced Sudoku" "/sudoku-pro"');
        console.log('  node auto-deploy-game.js --custom config.json');
        return;
    }
    
    if (args[0] === '--template' && args.length >= 6) {
        const [, template, name, icon, description, url] = args;
        
        switch (template) {
            case 'card':
                deployCardGame(name, icon, description, url);
                break;
            case 'puzzle':
                deployPuzzleGame(name, icon, description, url);
                break;
            case 'arcade':
                deployArcadeGame(name, icon, description, url);
                break;
            default:
                console.error('❌ Unknown template. Available: card, puzzle, arcade');
        }
    } else if (args[0] === '--custom' && args[1]) {
        const configPath = path.resolve(args[1]);
        if (fs.existsSync(configPath)) {
            const gameConfig = JSON.parse(fs.readFileSync(configPath, 'utf8'));
            deployNewGame(gameConfig);
        } else {
            console.error(`❌ Config file not found: ${configPath}`);
        }
    } else {
        console.error('❌ Invalid arguments. Use --help for usage information.');
    }
}

// Export functions
module.exports = {
    deployNewGame,
    deployCardGame,
    deployPuzzleGame,
    deployArcadeGame,
    validateGameConfig,
    generateGameKey,
    performBatchUpdate,
    generateDeploymentReport
};

// Run if called directly
if (require.main === module) {
    main();
}
