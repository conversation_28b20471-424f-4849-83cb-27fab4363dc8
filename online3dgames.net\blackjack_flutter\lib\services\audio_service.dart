import 'package:audioplayers/audioplayers.dart';
import 'package:shared_preferences/shared_preferences.dart';

enum SoundType {
  cardDeal,
  cardFlip,
  chipPlace,
  win,
  lose,
  shuffle,
  button,
}

class AudioService {
  static final AudioService _instance = AudioService._internal();
  factory AudioService() => _instance;
  AudioService._internal();

  final Map<SoundType, AudioPlayer> _players = {};
  bool _soundEnabled = true;
  double _volume = 0.7;
  bool _initialized = false;

  // 初始化音频服务
  Future<void> initialize() async {
    if (_initialized) return;

    try {
      // 加载设置
      await _loadSettings();
      
      // 预加载音频文件
      await _preloadSounds();
      
      _initialized = true;
    } catch (e) {
      print('Failed to initialize audio service: $e');
    }
  }

  // 预加载音频文件
  Future<void> _preloadSounds() async {
    final Map<SoundType, String> soundFiles = {
      SoundType.cardDeal: 'assets/audio/deal.mp3',
      SoundType.cardFlip: 'assets/audio/deal.mp3',
      SoundType.chipPlace: 'assets/audio/deal.mp3',
      SoundType.win: 'assets/audio/win.mp3',
      SoundType.lose: 'assets/audio/deal.mp3',
      SoundType.shuffle: 'assets/audio/deal.mp3',
      SoundType.button: 'assets/audio/deal.mp3',
    };

    for (final entry in soundFiles.entries) {
      try {
        final player = AudioPlayer();
        await player.setSource(AssetSource(entry.value.replaceFirst('assets/', '')));
        await player.setVolume(_soundEnabled ? _volume : 0);
        _players[entry.key] = player;
      } catch (e) {
        print('Failed to preload sound ${entry.key}: $e');
      }
    }
  }

  // 播放音效
  Future<void> playSound(SoundType soundType) async {
    if (!_soundEnabled || !_initialized) return;

    try {
      final player = _players[soundType];
      if (player != null) {
        await player.stop();
        await player.resume();
      }
    } catch (e) {
      print('Failed to play sound $soundType: $e');
    }
  }

  // 设置音量
  Future<void> setVolume(double volume) async {
    _volume = volume.clamp(0.0, 1.0);
    
    for (final player in _players.values) {
      await player.setVolume(_soundEnabled ? _volume : 0);
    }
    
    await _saveSettings();
  }

  // 切换音效开关
  Future<void> toggleSound() async {
    _soundEnabled = !_soundEnabled;
    
    for (final player in _players.values) {
      await player.setVolume(_soundEnabled ? _volume : 0);
    }
    
    await _saveSettings();
  }

  // 设置音效开关
  Future<void> setSoundEnabled(bool enabled) async {
    _soundEnabled = enabled;
    
    for (final player in _players.values) {
      await player.setVolume(_soundEnabled ? _volume : 0);
    }
    
    await _saveSettings();
  }

  // 获取当前设置
  bool get soundEnabled => _soundEnabled;
  double get volume => _volume;

  // 加载设置
  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _soundEnabled = prefs.getBool('sound_enabled') ?? true;
      _volume = prefs.getDouble('sound_volume') ?? 0.7;
    } catch (e) {
      print('Failed to load audio settings: $e');
    }
  }

  // 保存设置
  Future<void> _saveSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('sound_enabled', _soundEnabled);
      await prefs.setDouble('sound_volume', _volume);
    } catch (e) {
      print('Failed to save audio settings: $e');
    }
  }

  // 测试音效
  Future<void> testSound() async {
    await playSound(SoundType.cardDeal);
  }

  // 释放资源
  Future<void> dispose() async {
    for (final player in _players.values) {
      await player.dispose();
    }
    _players.clear();
    _initialized = false;
  }

  // 暂停所有音效
  Future<void> pauseAll() async {
    for (final player in _players.values) {
      await player.pause();
    }
  }

  // 恢复所有音效
  Future<void> resumeAll() async {
    if (!_soundEnabled) return;
    
    for (final player in _players.values) {
      await player.resume();
    }
  }

  // 停止所有音效
  Future<void> stopAll() async {
    for (final player in _players.values) {
      await player.stop();
    }
  }
}

// 音效管理器的便捷方法
class SoundManager {
  static final AudioService _audioService = AudioService();

  static Future<void> initialize() async {
    await _audioService.initialize();
  }

  static Future<void> playCardDeal() async {
    await _audioService.playSound(SoundType.cardDeal);
  }

  static Future<void> playCardFlip() async {
    await _audioService.playSound(SoundType.cardFlip);
  }

  static Future<void> playChipPlace() async {
    await _audioService.playSound(SoundType.chipPlace);
  }

  static Future<void> playWin() async {
    await _audioService.playSound(SoundType.win);
  }

  static Future<void> playLose() async {
    await _audioService.playSound(SoundType.lose);
  }

  static Future<void> playShuffle() async {
    await _audioService.playSound(SoundType.shuffle);
  }

  static Future<void> playButton() async {
    await _audioService.playSound(SoundType.button);
  }

  static Future<void> setVolume(double volume) async {
    await _audioService.setVolume(volume);
  }

  static Future<void> toggleSound() async {
    await _audioService.toggleSound();
  }

  static bool get soundEnabled => _audioService.soundEnabled;
  static double get volume => _audioService.volume;
}
