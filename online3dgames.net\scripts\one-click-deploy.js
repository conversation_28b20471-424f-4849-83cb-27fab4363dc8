#!/usr/bin/env node

// One-Click Game Deployment System
// Complete automation: template generation + configuration + page updates

const fs = require('fs');
const path = require('path');
const readline = require('readline');

// Import all required modules
const { generateCompleteGameTemplate } = require('./game-page-generator.js');
const { deployNewGame } = require('./auto-deploy-game.js');
const { addNewGame } = require('./game-config.js');

// Configuration
const BASE_DIR = path.dirname(__dirname);

// Interactive CLI
const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

function askQuestion(question) {
    return new Promise((resolve) => {
        rl.question(question, (answer) => {
            resolve(answer.trim());
        });
    });
}

// Predefined game templates for quick setup
const quickTemplates = {
    'card': {
        category: 'card-games',
        difficulty: '⭐⭐⭐',
        rating: '4.5',
        tags: ['Cards', 'Strategy', 'Classic'],
        seoSuffix: 'Classic Card Game'
    },
    'puzzle': {
        category: 'puzzle',
        difficulty: '⭐⭐⭐',
        rating: '4.6',
        tags: ['Puzzle', 'Logic', 'Brain Training'],
        seoSuffix: 'Brain Training Puzzle Game'
    },
    'arcade': {
        category: 'arcade',
        difficulty: '⭐⭐',
        rating: '4.4',
        tags: ['Arcade', 'Action', 'Classic'],
        seoSuffix: 'Classic Arcade Game'
    },
    'relaxation': {
        category: 'relaxation',
        difficulty: '⭐',
        rating: '4.3',
        tags: ['Relaxation', 'Stress Relief', 'Meditation'],
        seoSuffix: 'Relaxing Game for Stress Relief'
    },
    'blackjack': {
        category: 'blackjack',
        difficulty: '⭐⭐⭐⭐',
        rating: '4.7',
        tags: ['Casino', 'Strategy', 'Cards'],
        seoSuffix: 'Casino Card Game'
    },
    'board': {
        category: 'board-games',
        difficulty: '⭐⭐⭐⭐',
        rating: '4.8',
        tags: ['Strategy', 'Board Game', 'Classic'],
        seoSuffix: 'Strategic Board Game'
    }
};

// Main deployment workflow
async function oneClickDeploy() {
    console.log('🚀 One-Click Game Deployment System\n');
    console.log('This will guide you through creating a complete game setup:\n');
    console.log('✅ Generate game page template');
    console.log('✅ Add to game configuration');
    console.log('✅ Update all related pages');
    console.log('✅ Generate SEO content');
    console.log('✅ Update sitemap\n');
    
    try {
        // Step 1: Choose deployment method
        const method = await chooseDeploymentMethod();
        
        let gameConfig;
        if (method === 'quick') {
            gameConfig = await quickSetup();
        } else if (method === 'custom') {
            gameConfig = await customSetup();
        } else {
            gameConfig = await loadConfigFile();
        }
        
        // Step 2: Validate configuration
        if (!validateConfig(gameConfig)) {
            console.log('❌ Configuration validation failed');
            rl.close();
            return;
        }
        
        // Step 3: Show summary and confirm
        await showSummaryAndConfirm(gameConfig);
        
        // Step 4: Execute deployment
        await executeDeployment(gameConfig);
        
        console.log('\n🎉 One-click deployment completed successfully!');
        
    } catch (error) {
        console.error(`❌ Deployment failed: ${error.message}`);
    } finally {
        rl.close();
    }
}

async function chooseDeploymentMethod() {
    console.log('Choose deployment method:');
    console.log('1. Quick Setup (use predefined templates)');
    console.log('2. Custom Setup (manual configuration)');
    console.log('3. Load from file (JSON config)');
    
    const choice = await askQuestion('\nSelect option (1-3): ');
    
    switch (choice) {
        case '1': return 'quick';
        case '2': return 'custom';
        case '3': return 'file';
        default:
            console.log('Invalid choice, using quick setup');
            return 'quick';
    }
}

async function quickSetup() {
    console.log('\n📋 Quick Setup\n');
    
    // Show available templates
    console.log('Available templates:');
    Object.keys(quickTemplates).forEach((key, index) => {
        const template = quickTemplates[key];
        console.log(`${index + 1}. ${key} (${template.category}) - ${template.seoSuffix}`);
    });
    
    const templateChoice = await askQuestion('\nSelect template (1-6): ');
    const templateKeys = Object.keys(quickTemplates);
    const templateKey = templateKeys[parseInt(templateChoice) - 1];
    
    if (!templateKey) {
        throw new Error('Invalid template selection');
    }
    
    const template = quickTemplates[templateKey];
    
    // Get basic game info
    const name = await askQuestion('Game name: ');
    const icon = await askQuestion('Game icon (emoji): ');
    const description = await askQuestion('Game description: ');
    
    // Generate URL from name
    const url = '/' + name.toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim();
    
    // Build complete config
    const gameConfig = {
        name,
        icon,
        description,
        url,
        category: template.category,
        difficulty: template.difficulty,
        rating: template.rating,
        tags: template.tags,
        seoTitle: `${name} Online - ${template.seoSuffix} | Flow Fray`,
        seoDescription: `Play ${name} online for free. ${description} No downloads required, play instantly in your browser.`
    };
    
    return gameConfig;
}

async function customSetup() {
    console.log('\n⚙️ Custom Setup\n');
    
    const name = await askQuestion('Game name: ');
    const icon = await askQuestion('Game icon (emoji): ');
    const description = await askQuestion('Game description: ');
    const url = await askQuestion(`Game URL (default: /${name.toLowerCase().replace(/\s+/g, '-')}): `) || 
                 `/${name.toLowerCase().replace(/\s+/g, '-')}`;
    
    // Show categories
    console.log('\nAvailable categories:');
    Object.keys(quickTemplates).forEach((key, index) => {
        console.log(`${index + 1}. ${key}`);
    });
    
    const categoryChoice = await askQuestion('Select category (1-6): ');
    const categoryKeys = Object.keys(quickTemplates);
    const categoryKey = categoryKeys[parseInt(categoryChoice) - 1];
    const template = quickTemplates[categoryKey] || quickTemplates['puzzle'];
    
    const rating = await askQuestion(`Rating (default: ${template.rating}): `) || template.rating;
    const difficulty = await askQuestion(`Difficulty (default: ${template.difficulty}): `) || template.difficulty;
    const tags = (await askQuestion(`Tags (comma-separated, default: ${template.tags.join(', ')}): `) || 
                  template.tags.join(', ')).split(',').map(tag => tag.trim());
    
    const seoTitle = await askQuestion(`SEO Title (default: ${name} Online - Free Game | Flow Fray): `) || 
                     `${name} Online - Free Game | Flow Fray`;
    const seoDescription = await askQuestion(`SEO Description: `) || 
                          `Play ${name} online for free. ${description}`;
    
    return {
        name,
        icon,
        description,
        url,
        category: template.category,
        difficulty,
        rating,
        tags,
        seoTitle,
        seoDescription
    };
}

async function loadConfigFile() {
    const filePath = await askQuestion('Enter config file path: ');
    const fullPath = path.resolve(filePath);
    
    if (!fs.existsSync(fullPath)) {
        throw new Error(`Config file not found: ${fullPath}`);
    }
    
    const config = JSON.parse(fs.readFileSync(fullPath, 'utf8'));
    console.log('✅ Configuration loaded from file');
    
    return config;
}

function validateConfig(config) {
    const required = ['name', 'icon', 'description', 'url', 'category'];
    
    for (const field of required) {
        if (!config[field]) {
            console.error(`❌ Missing required field: ${field}`);
            return false;
        }
    }
    
    if (!config.url.startsWith('/')) {
        console.error('❌ URL must start with "/"');
        return false;
    }
    
    return true;
}

async function showSummaryAndConfirm(config) {
    console.log('\n📋 Deployment Summary:\n');
    console.log(`Name: ${config.name}`);
    console.log(`Icon: ${config.icon}`);
    console.log(`URL: ${config.url}`);
    console.log(`Category: ${config.category}`);
    console.log(`Rating: ${config.rating}`);
    console.log(`Difficulty: ${config.difficulty}`);
    console.log(`Tags: ${config.tags.join(', ')}`);
    console.log(`SEO Title: ${config.seoTitle}`);
    console.log(`Description: ${config.description}`);
    
    console.log('\n📁 Files that will be created:');
    const gameKey = config.url.substring(1);
    console.log(`  ${gameKey}/index.html`);
    console.log(`  ${gameKey}/css/styles.css`);
    console.log(`  ${gameKey}/js/game.js`);
    console.log(`  ${gameKey}/images/ (directory)`);
    console.log(`  ${gameKey}/audio/ (directory)`);
    
    console.log('\n🔄 Updates that will be performed:');
    console.log('  ✅ Add game to configuration');
    console.log('  ✅ Update all game page recommendations');
    console.log('  ✅ Update homepage categories');
    console.log('  ✅ Update sitemap.xml');
    console.log('  ✅ Create deployment backup');
    
    const confirm = await askQuestion('\nProceed with deployment? (y/n): ');
    if (confirm.toLowerCase() !== 'y') {
        throw new Error('Deployment cancelled by user');
    }
}

async function executeDeployment(config) {
    console.log('\n🚀 Executing deployment...\n');
    
    try {
        // Step 1: Generate game template
        console.log('📝 Generating game page template...');
        const gameDir = generateCompleteGameTemplate(config);
        
        // Step 2: Deploy to system
        console.log('\n🔧 Adding to game system...');
        const deploySuccess = deployNewGame(config);
        
        if (deploySuccess) {
            console.log('\n✅ Deployment completed successfully!');
            
            // Show next steps
            console.log('\n📋 Next Steps:');
            console.log(`1. Edit ${config.url.substring(1)}/js/game.js to implement game logic`);
            console.log(`2. Customize ${config.url.substring(1)}/css/styles.css for game-specific styling`);
            console.log(`3. Add game assets to ${config.url.substring(1)}/images/ and ${config.url.substring(1)}/audio/`);
            console.log(`4. Test the game at https://flowfray.com${config.url}`);
            console.log('5. Verify recommendations appear on other game pages');
            console.log('6. Check homepage categories include the new game');
            
        } else {
            throw new Error('System deployment failed');
        }
        
    } catch (error) {
        console.error(`❌ Deployment error: ${error.message}`);
        throw error;
    }
}

// Batch deployment from directory
async function batchDeploy() {
    const configDir = await askQuestion('Enter directory containing config files: ');
    const fullDir = path.resolve(configDir);
    
    if (!fs.existsSync(fullDir)) {
        console.error(`❌ Directory not found: ${fullDir}`);
        return;
    }
    
    const files = fs.readdirSync(fullDir).filter(file => file.endsWith('.json'));
    
    if (files.length === 0) {
        console.log('❌ No JSON config files found');
        return;
    }
    
    console.log(`\n📦 Found ${files.length} config files`);
    const confirm = await askQuestion('Deploy all games? (y/n): ');
    
    if (confirm.toLowerCase() !== 'y') {
        console.log('Batch deployment cancelled');
        return;
    }
    
    let successCount = 0;
    
    for (const file of files) {
        try {
            console.log(`\n🚀 Deploying ${file}...`);
            const config = JSON.parse(fs.readFileSync(path.join(fullDir, file), 'utf8'));
            
            if (validateConfig(config)) {
                generateCompleteGameTemplate(config);
                if (deployNewGame(config)) {
                    successCount++;
                    console.log(`✅ ${config.name} deployed successfully`);
                } else {
                    console.log(`❌ ${config.name} deployment failed`);
                }
            } else {
                console.log(`❌ ${file} has invalid configuration`);
            }
        } catch (error) {
            console.error(`❌ Error deploying ${file}: ${error.message}`);
        }
    }
    
    console.log(`\n📊 Batch deployment completed: ${successCount}/${files.length} successful`);
}

// Main menu
async function showMainMenu() {
    console.log('\n🎮 One-Click Game Deployment System\n');
    console.log('1. Deploy single game (interactive)');
    console.log('2. Deploy from config file');
    console.log('3. Batch deploy from directory');
    console.log('4. Exit');
    
    const choice = await askQuestion('\nSelect option (1-4): ');
    
    switch (choice) {
        case '1':
            await oneClickDeploy();
            break;
        case '2':
            const config = await loadConfigFile();
            if (validateConfig(config)) {
                await showSummaryAndConfirm(config);
                await executeDeployment(config);
            }
            break;
        case '3':
            await batchDeploy();
            break;
        case '4':
            console.log('👋 Goodbye!');
            rl.close();
            return;
        default:
            console.log('Invalid option');
    }
    
    if (choice !== '4') {
        const again = await askQuestion('\nDeploy another game? (y/n): ');
        if (again.toLowerCase() === 'y') {
            await showMainMenu();
        } else {
            rl.close();
        }
    }
}

// Export functions
module.exports = {
    oneClickDeploy,
    quickSetup,
    customSetup,
    executeDeployment,
    batchDeploy,
    showMainMenu
};

// Run if called directly
if (require.main === module) {
    showMainMenu().catch(console.error);
}
