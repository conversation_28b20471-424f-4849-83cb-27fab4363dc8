/**
 * Mahjong Solitaire - Tile System
 * International standard Mahjong Solitaire game
 */

class MahjongTile {
    constructor(type, value, unicode, id) {
        this.type = type;           // 'wan', 'tong', 'tiao', 'feng', 'jian', 'hua', 'season'
        this.value = value;         // 1-9 for numbered tiles, specific values for others
        this.unicode = unicode;     // Unicode character for display
        this.id = id;              // Unique identifier
        this.row = 0;              // Grid row position
        this.col = 0;              // Grid column position
        this.layer = 0;            // Layer position (0 = bottom)
        this.x = 0;                // Pixel X position
        this.y = 0;                // Pixel Y position
        this.element = null;       // DOM element
        this.selected = false;     // Currently selected
        this.removed = false;      // Has been removed
        this.blocked = false;      // Blocked by other tiles
    }

    /**
     * Check if this tile can match with another tile
     */
    canMatch(otherTile) {
        if (!otherTile || this.removed || otherTile.removed) {
            return false;
        }

        // Exact match for most tiles
        if (this.type === otherTile.type && this.value === otherTile.value) {
            return true;
        }

        // Special matching rules for flower and season tiles
        if (this.type === 'hua' && otherTile.type === 'hua') {
            return true; // Any flower matches any flower
        }

        if (this.type === 'season' && otherTile.type === 'season') {
            return true; // Any season matches any season
        }

        return false;
    }

    /**
     * Get CSS classes for this tile
     */
    getCSSClasses() {
        const classes = ['mahjong-tile', `tile-${this.type}`];

        if (this.selected) classes.push('selected');
        if (this.removed) classes.push('removed');
        if (this.blocked) classes.push('blocked');

        return classes.join(' ');
    }

    /**
     * Create DOM element for this tile
     */
    createElement() {
        // Always create a new element to avoid event conflicts
        const element = document.createElement('div');
        element.className = this.getCSSClasses();
        element.textContent = this.unicode;
        element.dataset.tileId = this.id;
        element.dataset.type = this.type;
        element.dataset.value = this.value;

        // Position the element
        element.style.left = `${this.x}px`;
        element.style.top = `${this.y}px`;
        element.style.zIndex = this.layer * 10 + 1;

        this.element = element;
        return element;
    }

    /**
     * Update tile position
     */
    setPosition(x, y) {
        this.x = x;
        this.y = y;

        if (this.element) {
            this.element.style.left = `${x}px`;
            this.element.style.top = `${y}px`;
            this.element.style.zIndex = this.layer * 10 + 1;
        }
    }

    /**
     * Select/deselect tile
     */
    setSelected(selected) {
        this.selected = selected;
        if (this.element) {
            this.element.className = this.getCSSClasses();
        }
    }

    /**
     * Remove tile from game
     */
    remove() {
        this.removed = true;
        if (this.element) {
            this.element.classList.add('removed');
            setTimeout(() => {
                if (this.element && this.element.parentNode) {
                    this.element.parentNode.removeChild(this.element);
                }
            }, 300);
        }
    }

    /**
     * Check if tile is free (can be selected)
     */
    isFree() {
        return !this.removed && !this.blocked;
    }
}

/**
 * Mahjong Tile Set Generator
 */
class MahjongTileSet {
    constructor() {
        this.tiles = [];
        this.tileCounter = 0;
        this.layout = null;
    }

    /**
     * Generate solvable Mahjong Solitaire tile set
     * Creates pairs to ensure the game is always solvable
     */
    generateTiles() {
        this.tiles = [];
        this.tileCounter = 0;

        // Simplified tile set for better gameplay - ensure even numbers for pairing
        const tileTypes = [
            // Wan (Characters) - 9 types × 2 = 18 tiles
            { type: 'wan', unicodes: ['🀇', '🀈', '🀉', '🀊', '🀋', '🀌', '🀍', '🀎', '🀏'], count: 2 },
            // Tong (Circles) - 9 types × 2 = 18 tiles
            { type: 'tong', unicodes: ['🀙', '🀚', '🀛', '🀜', '🀝', '🀞', '🀟', '🀠', '🀡'], count: 2 },
            // Tiao (Bamboos) - 9 types × 2 = 18 tiles
            { type: 'tiao', unicodes: ['🀐', '🀑', '🀒', '🀓', '🀔', '🀕', '🀖', '🀗', '🀘'], count: 2 },
            // Wind tiles - 4 types × 2 = 8 tiles
            { type: 'feng', unicodes: ['🀀', '🀁', '🀂', '🀃'], count: 2 },
            // Dragon tiles - 3 types × 2 = 6 tiles
            { type: 'jian', unicodes: ['🀄', '🀅', '🀆'], count: 2 },
            // Flower tiles - 4 types × 2 = 8 tiles (treat as pairs)
            { type: 'hua', unicodes: ['🀢', '🀣', '🀤', '🀥'], count: 2 },
            // Season tiles - 4 types × 2 = 8 tiles (treat as pairs)
            { type: 'season', unicodes: ['🀦', '🀧', '🀨', '🀩'], count: 2 }
        ];
        // Total: 18 + 18 + 18 + 8 + 6 + 8 + 8 = 84 tiles (42 pairs)

        // Generate tiles in pairs to ensure solvability
        tileTypes.forEach(typeData => {
            typeData.unicodes.forEach((unicode, index) => {
                for (let copy = 0; copy < typeData.count; copy++) {
                    const tile = new MahjongTile(
                        typeData.type,
                        index + 1,
                        unicode,
                        this.tileCounter++
                    );
                    this.tiles.push(tile);
                }
            });
        });

        return this.tiles;
    }

    /**
     * Shuffle tiles array
     */
    shuffle() {
        for (let i = this.tiles.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [this.tiles[i], this.tiles[j]] = [this.tiles[j], this.tiles[i]];
        }
        return this.tiles;
    }

    /**
     * Get tile by ID
     */
    getTileById(id) {
        return this.tiles.find(tile => tile.id === parseInt(id));
    }

    /**
     * Get all active (non-removed) tiles
     */
    getActiveTiles() {
        return this.tiles.filter(tile => !tile.removed);
    }

    /**
     * Check if all tiles are removed
     */
    isComplete() {
        return this.tiles.every(tile => tile.removed);
    }

    /**
     * Remove a tile from the game
     */
    removeTile(tile) {
        tile.remove();
    }
}
