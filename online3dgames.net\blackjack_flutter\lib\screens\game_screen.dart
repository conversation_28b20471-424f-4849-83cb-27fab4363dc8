import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:vibration/vibration.dart';
import '../providers/game_provider.dart';
import '../models/game_state.dart';
import '../widgets/playing_card_widget.dart';
import '../widgets/chip_widget.dart';
import '../services/audio_service.dart';

class GameScreen extends StatefulWidget {
  const GameScreen({super.key});

  @override
  State<GameScreen> createState() => _GameScreenState();
}

class _GameScreenState extends State<GameScreen> {
  int? selectedChipValue;
  final List<int> availableChips = [5, 10, 25, 50, 100];

  @override
  void initState() {
    super.initState();
    selectedChipValue = availableChips.first;
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<GameProvider>().initializeGame();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF1a4b3a),
              Color(0xFF0d2818),
            ],
          ),
        ),
        child: SafeArea(
          child: Consumer<GameProvider>(
            builder: (context, gameProvider, child) {
              return Column(
                children: [
                  _buildTopBar(gameProvider),
                  Expanded(
                    child: _buildGameTable(gameProvider),
                  ),
                  _buildBottomControls(gameProvider),
                ],
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildTopBar(GameProvider gameProvider) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // 余额显示
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.3),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(color: Colors.yellow, width: 1),
            ),
            child: Row(
              children: [
                const Icon(Icons.account_balance_wallet, color: Colors.yellow, size: 20),
                const SizedBox(width: 8),
                Text(
                  '\$${gameProvider.balance}',
                  style: const TextStyle(
                    color: Colors.yellow,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          
          // 牌组信息
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.3),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Row(
              children: [
                const Icon(Icons.style, color: Colors.white70, size: 20),
                const SizedBox(width: 8),
                Text(
                  '${gameProvider.remainingCards} cards',
                  style: const TextStyle(
                    color: Colors.white70,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          
          // 设置按钮
          IconButton(
            onPressed: () => _showSettingsDialog(),
            icon: const Icon(Icons.settings, color: Colors.white70),
          ),
        ],
      ),
    );
  }

  Widget _buildGameTable(GameProvider gameProvider) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // 庄家区域
          _buildDealerArea(gameProvider),
          
          const SizedBox(height: 40),
          
          // 游戏状态信息
          _buildGameStatus(gameProvider),
          
          const SizedBox(height: 40),
          
          // 玩家区域
          _buildPlayerArea(gameProvider),
        ],
      ),
    );
  }

  Widget _buildDealerArea(GameProvider gameProvider) {
    final dealer = gameProvider.dealer;
    
    return Column(
      children: [
        const Text(
          'Dealer',
          style: TextStyle(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        
        // 庄家分数
        if (dealer.cards.isNotEmpty && !dealer.cards.any((card) => card.isHidden))
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.5),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              dealer.currentScore.toString(),
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        
        const SizedBox(height: 12),
        
        // 庄家手牌
        SizedBox(
          height: 100,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: dealer.cards.asMap().entries.map((entry) {
              final index = entry.key;
              final card = entry.value;
              
              return Padding(
                padding: EdgeInsets.only(left: index > 0 ? 8 : 0),
                child: PlayingCardWidget(
                  card: card,
                  width: 70,
                  height: 98,
                  isAnimated: true,
                  animationDelay: Duration(milliseconds: index * 300),
                ),
              );
            }).toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildGameStatus(GameProvider gameProvider) {
    final statusMessage = gameProvider.gameState.statusMessage ?? '';
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.4),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: Colors.white24),
      ),
      child: Text(
        statusMessage,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 16,
          fontWeight: FontWeight.w500,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  Widget _buildPlayerArea(GameProvider gameProvider) {
    final player = gameProvider.humanPlayer;
    
    return Column(
      children: [
        // 玩家手牌
        SizedBox(
          height: 100,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: player.cards.asMap().entries.map((entry) {
              final index = entry.key;
              final card = entry.value;
              
              return Padding(
                padding: EdgeInsets.only(left: index > 0 ? 8 : 0),
                child: PlayingCardWidget(
                  card: card,
                  width: 70,
                  height: 98,
                  isAnimated: true,
                  animationDelay: Duration(milliseconds: (index + 2) * 300),
                ),
              );
            }).toList(),
          ),
        ),
        
        const SizedBox(height: 12),
        
        // 玩家分数和下注
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // 分数
            if (player.cards.isNotEmpty)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.blue.withOpacity(0.7),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  player.currentScore.toString(),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            
            if (player.cards.isNotEmpty && player.bet > 0)
              const SizedBox(width: 20),
            
            // 下注金额
            if (player.bet > 0)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.green.withOpacity(0.7),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(Icons.casino, color: Colors.white, size: 16),
                    const SizedBox(width: 4),
                    Text(
                      '\$${player.bet}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
          ],
        ),
        
        const SizedBox(height: 8),
        
        const Text(
          'You',
          style: TextStyle(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _buildBottomControls(GameProvider gameProvider) {
    final phase = gameProvider.phase;
    
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // 游戏操作按钮
          if (phase == GamePhase.playing)
            _buildGameActionButtons(gameProvider)
          else if (phase == GamePhase.betting)
            _buildBettingControls(gameProvider),
          
          const SizedBox(height: 16),
          
          // 筹码选择器
          if (phase == GamePhase.betting)
            ChipSelectorWidget(
              availableChips: availableChips,
              selectedChip: selectedChipValue,
              onChipSelected: (value) {
                setState(() {
                  selectedChipValue = value;
                });
                _hapticFeedback();
              },
              enabled: gameProvider.canBet,
            ),
        ],
      ),
    );
  }

  Widget _buildGameActionButtons(GameProvider gameProvider) {
    final player = gameProvider.humanPlayer;

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        // Hit按钮
        _buildActionButton(
          icon: Icons.add,
          label: 'Hit',
          onPressed: () async {
            await gameProvider.hit();
            _hapticFeedback();
          },
          color: Colors.blue,
        ),

        // Stand按钮
        _buildActionButton(
          icon: Icons.stop,
          label: 'Stand',
          onPressed: () async {
            await gameProvider.stand();
            _hapticFeedback();
          },
          color: Colors.red,
        ),

        // Double按钮
        if (player.canDouble && gameProvider.balance >= player.bet)
          _buildActionButton(
            icon: Icons.keyboard_double_arrow_up,
            label: 'Double',
            onPressed: () async {
              await gameProvider.doubleDown();
              _hapticFeedback();
            },
            color: Colors.orange,
          ),

        // Split按钮
        if (player.canSplit && gameProvider.balance >= player.bet)
          _buildActionButton(
            icon: Icons.call_split,
            label: 'Split',
            onPressed: () async {
              await gameProvider.split();
              _hapticFeedback();
            },
            color: Colors.purple,
          ),

        // Surrender按钮
        if (player.canSurrender)
          _buildActionButton(
            icon: Icons.flag,
            label: 'Surrender',
            onPressed: () async {
              await gameProvider.surrender();
              _hapticFeedback();
            },
            color: Colors.grey,
          ),
      ],
    );
  }

  Widget _buildBettingControls(GameProvider gameProvider) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        // 下注按钮
        _buildActionButton(
          icon: Icons.add_circle,
          label: 'Bet \$${selectedChipValue ?? 5}',
          onPressed: gameProvider.canBet && selectedChipValue != null
              ? () async {
                  await gameProvider.placeBet(selectedChipValue!);
                  _hapticFeedback();
                }
              : null,
          color: Colors.green,
        ),

        // 清除下注按钮
        if (gameProvider.humanPlayer.bet > 0)
          _buildActionButton(
            icon: Icons.clear,
            label: 'Clear',
            onPressed: () async {
              await gameProvider.clearBet();
              _hapticFeedback();
            },
            color: Colors.red,
          ),

        // 开始游戏按钮
        if (gameProvider.canStartGame)
          _buildActionButton(
            icon: Icons.play_arrow,
            label: 'Deal',
            onPressed: () async {
              await gameProvider.startNewGame();
              _hapticFeedback();
            },
            color: Colors.blue,
            isPrimary: true,
          ),
      ],
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback? onPressed,
    required Color color,
    bool isPrimary = false,
  }) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, size: 20),
      label: Text(
        label,
        style: TextStyle(
          fontSize: isPrimary ? 16 : 14,
          fontWeight: isPrimary ? FontWeight.bold : FontWeight.normal,
        ),
      ),
      style: ElevatedButton.styleFrom(
        backgroundColor: color,
        foregroundColor: Colors.white,
        padding: EdgeInsets.symmetric(
          horizontal: isPrimary ? 24 : 16,
          vertical: isPrimary ? 12 : 8,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(25),
        ),
        elevation: 4,
      ),
    );
  }

  void _hapticFeedback() {
    Vibration.hasVibrator().then((hasVibrator) {
      if (hasVibrator == true) {
        Vibration.vibrate(duration: 50);
      }
    });
  }

  void _showSettingsDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Settings'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.volume_up),
              title: const Text('Sound Effects'),
              trailing: Switch(
                value: SoundManager.soundEnabled,
                onChanged: (value) async {
                  await SoundManager.toggleSound();
                  setState(() {});
                },
              ),
            ),
            ListTile(
              leading: const Icon(Icons.refresh),
              title: const Text('Restart Game'),
              onTap: () {
                Navigator.of(context).pop();
                _showRestartConfirmation();
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showRestartConfirmation() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Restart Game'),
        content: const Text('Are you sure you want to restart? This will reset your balance to \$1000.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<GameProvider>().restartGame();
            },
            child: const Text('Restart'),
          ),
        ],
      ),
    );
  }
}
