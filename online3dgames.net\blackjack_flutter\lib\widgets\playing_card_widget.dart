import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../models/card.dart';

class PlayingCardWidget extends StatelessWidget {
  final PlayingCard? card;
  final double width;
  final double height;
  final bool isAnimated;
  final Duration animationDelay;
  final VoidCallback? onTap;

  const PlayingCardWidget({
    super.key,
    this.card,
    this.width = 60,
    this.height = 84,
    this.isAnimated = false,
    this.animationDelay = Duration.zero,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    Widget cardWidget = _buildCard(context);

    if (isAnimated) {
      cardWidget = cardWidget
          .animate(delay: animationDelay)
          .slideY(begin: -1, duration: 300.ms, curve: Curves.easeOut)
          .fadeIn(duration: 200.ms);
    }

    return GestureDetector(
      onTap: onTap,
      child: cardWidget,
    );
  }

  Widget _buildCard(BuildContext context) {
    if (card == null) {
      return _buildEmptySlot();
    }

    if (card!.isHidden) {
      return _buildCardBack();
    }

    return _buildCardFront();
  }

  Widget _buildEmptySlot() {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        border: Border.all(
          color: Colors.white.withOpacity(0.3),
          width: 2,
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Center(
        child: Icon(
          Icons.add,
          color: Colors.white54,
          size: 24,
        ),
      ),
    );
  }

  Widget _buildCardBack() {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFF1a4b3a),
            Color(0xFF0d2818),
          ],
        ),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.white24, width: 1),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.3),
            blurRadius: 4,
            offset: const Offset(2, 2),
          ),
        ],
      ),
      child: Stack(
        children: [
          // 背景图案
          Center(
            child: Container(
              width: width * 0.7,
              height: height * 0.7,
              decoration: BoxDecoration(
                border: Border.all(color: Colors.white24, width: 1),
                borderRadius: BorderRadius.circular(4),
              ),
              child: const Center(
                child: Icon(
                  Icons.casino,
                  color: Colors.white24,
                  size: 20,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCardFront() {
    final isRed = card!.color == CardColor.red;
    final cardColor = isRed ? Colors.red[700]! : Colors.black87;

    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!, width: 1),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 4,
            offset: const Offset(2, 2),
          ),
        ],
      ),
      child: Stack(
        children: [
          // 左上角
          Positioned(
            top: 4,
            left: 4,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  card!.displayValue,
                  style: TextStyle(
                    color: cardColor,
                    fontSize: width * 0.2,
                    fontWeight: FontWeight.bold,
                    height: 1.0,
                  ),
                ),
                Text(
                  card!.suitSymbol,
                  style: TextStyle(
                    color: cardColor,
                    fontSize: width * 0.15,
                    height: 1.0,
                  ),
                ),
              ],
            ),
          ),
          
          // 中央花色
          Center(
            child: Text(
              card!.suitSymbol,
              style: TextStyle(
                color: cardColor.withOpacity(0.3),
                fontSize: width * 0.4,
                height: 1.0,
              ),
            ),
          ),
          
          // 右下角（旋转180度）
          Positioned(
            bottom: 4,
            right: 4,
            child: Transform.rotate(
              angle: 3.14159, // 180度
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    card!.displayValue,
                    style: TextStyle(
                      color: cardColor,
                      fontSize: width * 0.2,
                      fontWeight: FontWeight.bold,
                      height: 1.0,
                    ),
                  ),
                  Text(
                    card!.suitSymbol,
                    style: TextStyle(
                      color: cardColor,
                      fontSize: width * 0.15,
                      height: 1.0,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// 卡牌翻转动画组件
class FlipCardWidget extends StatefulWidget {
  final PlayingCard card;
  final double width;
  final double height;
  final bool showFront;
  final Duration duration;

  const FlipCardWidget({
    super.key,
    required this.card,
    this.width = 60,
    this.height = 84,
    this.showFront = true,
    this.duration = const Duration(milliseconds: 600),
  });

  @override
  State<FlipCardWidget> createState() => _FlipCardWidgetState();
}

class _FlipCardWidgetState extends State<FlipCardWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(duration: widget.duration, vsync: this);
    _animation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );

    if (widget.showFront) {
      _controller.forward();
    }
  }

  @override
  void didUpdateWidget(FlipCardWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.showFront != oldWidget.showFront) {
      if (widget.showFront) {
        _controller.forward();
      } else {
        _controller.reverse();
      }
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        final isShowingFront = _animation.value > 0.5;
        return Transform(
          alignment: Alignment.center,
          transform: Matrix4.identity()
            ..setEntry(3, 2, 0.001)
            ..rotateY(_animation.value * 3.14159),
          child: isShowingFront
              ? PlayingCardWidget(
                  card: widget.card.copyWithHidden(false),
                  width: widget.width,
                  height: widget.height,
                )
              : PlayingCardWidget(
                  card: widget.card.copyWithHidden(true),
                  width: widget.width,
                  height: widget.height,
                ),
        );
      },
    );
  }
}
