/* Mahjong Connect Tiles Styles */

.mahjong-tile {
    width: 40px;
    height: 56px;
    background: linear-gradient(145deg, #fefefe 0%, #f0f0f0 100%);
    border: 2px solid #ccc;
    border-radius: 6px;
    position: absolute;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    box-shadow:
        2px 2px 6px rgba(0,0,0,0.3),
        inset 1px 1px 2px rgba(255,255,255,0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32px;
    line-height: 1;
    color: #333;
    overflow: hidden;
    user-select: none;
    z-index: 1;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}



.mahjong-tile:hover {
    transform: translateZ(var(--tile-z, 0px)) translateY(-2px) scale(1.05);
    box-shadow:
        4px 4px 16px rgba(0,0,0,0.5),
        2px 2px 8px rgba(0,0,0,0.3);
    filter: brightness(1.1);
}

/* Enhanced styling for cross-stacked tiles */
.mahjong-tile.cross-stacked {
    box-shadow:
        4px 4px 16px rgba(0,0,0,0.5),
        2px 2px 8px rgba(0,0,0,0.3),
        1px 1px 4px rgba(0,0,0,0.2),
        inset 1px 1px 3px rgba(255,255,255,0.9),
        inset -1px -1px 3px rgba(0,0,0,0.15);
    background: linear-gradient(145deg, #ffffff 0%, #f5f5f5 100%);
    border-color: #bbb;
    animation: subtleFloat 4s ease-in-out infinite;
}

.mahjong-tile.cross-stacked:hover {
    box-shadow:
        5px 5px 20px rgba(0,0,0,0.6),
        3px 3px 12px rgba(0,0,0,0.4),
        1px 1px 6px rgba(0,0,0,0.3);
    filter: brightness(1.15);
}

.mahjong-tile.selected {
    background: #fff8dc;
    border-color: #daa520;
    transform: translateY(-3px) scale(1.08);
    box-shadow:
        0 6px 12px rgba(218, 165, 32, 0.6),
        0 0 0 3px #daa520;
    color: #8b4513;
    z-index: 10;
}

.mahjong-tile.hint {
    animation: hintPulse 1s ease-in-out infinite;
    border-color: #ff6b6b;
    box-shadow:
        0 0 15px rgba(255, 107, 107, 0.8),
        0 0 30px rgba(255, 107, 107, 0.4);
}

@keyframes hintPulse {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.05);
        opacity: 0.8;
    }
}

.mahjong-tile.blocked {
    opacity: 0.4;
    cursor: not-allowed;
    filter: grayscale(50%);
    box-shadow:
        1px 1px 3px rgba(0,0,0,0.4),
        inset 0 0 0 1px rgba(0,0,0,0.2);
}

.mahjong-tile.blocked:hover {
    transform: none !important;
    opacity: 0.4 !important;
    box-shadow: 1px 1px 3px rgba(0,0,0,0.4) !important;
}



.mahjong-tile.removed {
    animation: tileRemove 0.3s ease-in-out forwards;
    z-index: 15;
}

@keyframes tileRemove {
    0% {
        transform: scale(1) rotate(0deg);
        opacity: 1;
    }
    50% {
        transform: scale(1.1) rotate(5deg);
        opacity: 0.7;
    }
    100% {
        transform: scale(0) rotate(15deg);
        opacity: 0;
    }
}

@keyframes tileRemove {
    0% {
        transform: scale(1) rotate(0deg);
        opacity: 1;
    }
    50% {
        transform: scale(1.2) rotate(5deg);
        opacity: 0.8;
    }
    100% {
        transform: scale(0) rotate(15deg);
        opacity: 0;
    }
}

.mahjong-tile.appearing {
    animation: tileAppear 0.5s ease-out forwards;
}

@keyframes tileAppear {
    0% {
        transform: scale(0) rotate(-15deg);
        opacity: 0;
    }
    50% {
        transform: scale(1.1) rotate(-5deg);
        opacity: 0.8;
    }
    100% {
        transform: scale(1) rotate(0deg);
        opacity: 1;
    }
}

/* Different types of mahjong tiles */
.tile-wan { 
    color: #d32f2f; 
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

.tile-tong { 
    color: #1976d2; 
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

.tile-tiao { 
    color: #388e3c; 
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

.tile-feng { 
    color: #7b1fa2; 
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

.tile-jian { 
    color: #d32f2f; 
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

.tile-hua { 
    color: #ff6f00; 
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

.tile-season { 
    color: #c2185b; 
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

/* Special color handling */
.tile-jian[data-value="fa"] {
    filter: hue-rotate(120deg) brightness(1.1) contrast(1.2);
}

.tile-special {
    background: linear-gradient(145deg, #fff8dc 0%, #f0e68c 50%, #daa520 100%);
    border-color: #b8860b;
}

.tile-back {
    background: linear-gradient(145deg, #2d5a2d 0%, #1a4d1a 100%);
    border-color: #1a4d1a;
    color: #ffffff;
    font-size: 32px;
    filter: none;
}

.tile-back::before {
    background: linear-gradient(135deg, #1a4d1a 0%, #0d2d0d 100%) !important;
}



/* Match effect */
.mahjong-tile.matching {
    animation: matchEffect 0.5s ease-in-out;
}

@keyframes matchEffect {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.2);
        box-shadow:
            0 0 20px rgba(76, 175, 80, 0.8),
            0 0 0 3px #4caf50;
    }
    100% {
        transform: scale(0);
        opacity: 0;
    }
}

/* Hint effect */
.mahjong-tile.hint-highlight {
    animation: hintPulse 1.5s ease-in-out infinite;
}

@keyframes hintPulse {
    0%, 100% {
        box-shadow: 2px 2px 6px rgba(0,0,0,0.3);
    }
    50% {
        box-shadow:
            0 0 20px rgba(255, 215, 0, 0.8),
            0 0 0 3px #ffd700;
        transform: translateY(-2px) scale(1.05);
    }
}

/* Shake animation for invalid connections */
@keyframes shake {
    0%, 100% {
        transform: translateX(0);
    }
    25% {
        transform: translateX(-5px);
    }
    75% {
        transform: translateX(5px);
    }
}

/* Mobile Landscape - Optimize tiles for landscape */
@media screen and (max-width: 768px) and (orientation: landscape) {
    .mahjong-tile {
        width: 40px;
        height: 56px;
        font-size: 32px;
        border-radius: 4px;
        transition: all 0.2s ease;
        touch-action: manipulation;
        -webkit-tap-highlight-color: transparent;
    }

    .mahjong-tile:hover,
    .mahjong-tile:active {
        transform: translateY(-1px) scale(1.02);
        box-shadow:
            2px 2px 6px rgba(0,0,0,0.4),
            inset 1px 1px 2px rgba(255,255,255,0.9);
    }

    .mahjong-tile.selected {
        transform: translateY(-2px) scale(1.05);
        box-shadow:
            0 0 12px rgba(255, 215, 0, 0.8),
            0 0 0 2px #ffd700,
            2px 2px 6px rgba(0,0,0,0.4);
    }

    .mahjong-tile.blocked {
        opacity: 0.5;
        filter: grayscale(0.4);
    }

    .tile-back {
        font-size: 24px;
    }

    /* Hint animation for mobile */
    .mahjong-tile.hint-highlight {
        animation: mobileHintPulse 1.2s ease-in-out infinite;
    }

    @keyframes mobileHintPulse {
        0%, 100% {
            box-shadow: 2px 2px 6px rgba(0,0,0,0.3);
        }
        50% {
            box-shadow:
                0 0 15px rgba(255, 215, 0, 0.9),
                0 0 0 2px #ffd700;
            transform: translateY(-1px) scale(1.03);
        }
    }
}

/* Small mobile landscape screens */
@media screen and (max-width: 480px) and (orientation: landscape) {
    .mahjong-tile {
        width: 36px;
        height: 50px;
        font-size: 28px;
    }

    .tile-back {
        font-size: 20px;
    }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .mahjong-tile {
        box-shadow: 
            1px 1px 2px rgba(0,0,0,0.3),
            inset 0 0 0 0.5px rgba(255,255,255,0.8);
    }
    
    .mahjong-tile::after {
        border-width: 0.5px;
    }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    .mahjong-tile,
    .mahjong-tile:hover,
    .mahjong-tile.selected {
        transition: none;
        animation: none;
    }
    
    .mahjong-tile.hint {
        animation: none;
        box-shadow: 
            0 0 10px rgba(255, 215, 0, 0.8),
            inset 0 0 0 2px #ffd700;
    }
}

/* Focus styles for keyboard navigation */
.mahjong-tile:focus {
    outline: 2px solid #ffd700;
    outline-offset: 2px;
}

/* Subtle floating animation for cross-stacked tiles */
@keyframes subtleFloat {
    0%, 100% {
        transform: translateZ(var(--tile-z, 0px)) translateY(0px);
    }
    50% {
        transform: translateZ(var(--tile-z, 0px)) translateY(-1px);
    }
}

/* Enhanced 3D depth animation */
@keyframes depthPulse {
    0%, 100% {
        box-shadow:
            4px 4px 16px rgba(0,0,0,0.5),
            2px 2px 8px rgba(0,0,0,0.3);
    }
    50% {
        box-shadow:
            6px 6px 20px rgba(0,0,0,0.6),
            3px 3px 12px rgba(0,0,0,0.4);
    }
}

/* Print styles */
@media print {
    .mahjong-tile {
        box-shadow: none;
        border: 2px solid #000;
        background: #fff;
        color: #000;
        animation: none;
    }
}
