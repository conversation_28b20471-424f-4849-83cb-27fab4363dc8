import 'dart:math';
import '../models/card.dart';

class DeckService {
  static final Random _random = Random();

  // 创建标准52张牌组
  static List<PlayingCard> createStandardDeck() {
    final List<PlayingCard> deck = [];
    
    for (final suit in Suit.values) {
      for (final value in CardValue.values) {
        deck.add(PlayingCard(suit: suit, value: value));
      }
    }
    
    return deck;
  }

  // 创建多副牌组
  static List<PlayingCard> createMultipleDeck(int deckCount) {
    final List<PlayingCard> multipleDeck = [];
    
    for (int i = 0; i < deckCount; i++) {
      multipleDeck.addAll(createStandardDeck());
    }
    
    return multipleDeck;
  }

  // Fisher-Yates洗牌算法
  static List<PlayingCard> shuffleDeck(List<PlayingCard> deck) {
    final List<PlayingCard> shuffledDeck = List.from(deck);
    
    for (int i = shuffledDeck.length - 1; i > 0; i--) {
      final int j = _random.nextInt(i + 1);
      final PlayingCard temp = shuffledDeck[i];
      shuffledDeck[i] = shuffledDeck[j];
      shuffledDeck[j] = temp;
    }
    
    return shuffledDeck;
  }

  // Riffle洗牌（更真实的洗牌方式）
  static List<PlayingCard> riffleShuffle(List<PlayingCard> deck) {
    if (deck.length < 2) return deck;
    
    List<PlayingCard> shuffledDeck = List.from(deck);
    
    // 执行多次riffle洗牌
    for (int shuffle = 0; shuffle < 7; shuffle++) {
      // 将牌组分成两半
      final int midPoint = shuffledDeck.length ~/ 2;
      final List<PlayingCard> leftHalf = shuffledDeck.sublist(0, midPoint);
      final List<PlayingCard> rightHalf = shuffledDeck.sublist(midPoint);
      
      shuffledDeck = _riffle(leftHalf, rightHalf);
    }
    
    return shuffledDeck;
  }

  // 执行单次riffle洗牌
  static List<PlayingCard> _riffle(List<PlayingCard> left, List<PlayingCard> right) {
    final List<PlayingCard> result = [];
    int leftIndex = 0;
    int rightIndex = 0;
    
    while (leftIndex < left.length || rightIndex < right.length) {
      // 随机决定从哪一半取牌
      final bool takeFromLeft = leftIndex < left.length && 
          (rightIndex >= right.length || _random.nextBool());
      
      if (takeFromLeft) {
        // 从左半部分取1-3张牌
        final int cardsToTake = _random.nextInt(3) + 1;
        final int actualTake = (leftIndex + cardsToTake).clamp(leftIndex, left.length);
        
        for (int i = leftIndex; i < actualTake; i++) {
          result.add(left[i]);
        }
        leftIndex = actualTake;
      } else {
        // 从右半部分取1-3张牌
        final int cardsToTake = _random.nextInt(3) + 1;
        final int actualTake = (rightIndex + cardsToTake).clamp(rightIndex, right.length);
        
        for (int i = rightIndex; i < actualTake; i++) {
          result.add(right[i]);
        }
        rightIndex = actualTake;
      }
    }
    
    return result;
  }

  // 发一张牌
  static PlayingCard? dealCard(List<PlayingCard> deck) {
    if (deck.isEmpty) return null;
    return deck.removeLast();
  }

  // 发多张牌
  static List<PlayingCard> dealCards(List<PlayingCard> deck, int count) {
    final List<PlayingCard> dealtCards = [];
    
    for (int i = 0; i < count && deck.isNotEmpty; i++) {
      final card = dealCard(deck);
      if (card != null) {
        dealtCards.add(card);
      }
    }
    
    return dealtCards;
  }

  // 将牌放入弃牌堆
  static List<PlayingCard> addToDiscardPile(
    List<PlayingCard> discardPile, 
    List<PlayingCard> cards
  ) {
    return [...discardPile, ...cards];
  }

  // 重新洗牌（将弃牌堆重新加入牌组）
  static Map<String, List<PlayingCard>> reshuffleWithDiscardPile(
    List<PlayingCard> deck,
    List<PlayingCard> discardPile,
  ) {
    final List<PlayingCard> allCards = [...deck, ...discardPile];
    final List<PlayingCard> shuffledDeck = riffleShuffle(allCards);
    
    return {
      'deck': shuffledDeck,
      'discardPile': <PlayingCard>[],
    };
  }

  // 检查牌组完整性
  static bool validateDeckIntegrity(
    List<PlayingCard> deck, 
    List<PlayingCard> discardPile,
    int expectedDeckCount,
  ) {
    final int totalCards = deck.length + discardPile.length;
    final int expectedTotal = expectedDeckCount * 52;
    
    return totalCards == expectedTotal;
  }

  // 获取牌组统计信息
  static Map<String, dynamic> getDeckStats(List<PlayingCard> deck) {
    final Map<Suit, int> suitCounts = {};
    final Map<CardValue, int> valueCounts = {};
    
    for (final card in deck) {
      suitCounts[card.suit] = (suitCounts[card.suit] ?? 0) + 1;
      valueCounts[card.value] = (valueCounts[card.value] ?? 0) + 1;
    }
    
    return {
      'totalCards': deck.length,
      'suitCounts': suitCounts,
      'valueCounts': valueCounts,
      'penetration': deck.length / 312.0, // 假设6副牌
    };
  }
}
