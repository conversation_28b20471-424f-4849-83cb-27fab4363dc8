const config = require('./game-config.js');

console.log('=== Card Games Category ===');
const cardGames = config.getGamesByCategory('card-games');
console.log('Total card games:', cardGames.length);
console.log('');

cardGames.forEach((game, i) => {
    console.log(`${i+1}. ${game.name} (${game.url})`);
});

console.log('\n=== Testing Spider Solitaire Recommendations ===');
const spiderGame = config.completeGameData['spider-solitaire'];
console.log('Spider Solitaire category:', spiderGame.category);

const sameCategory = cardGames.filter(g => g.url !== spiderGame.url);
console.log('Similar games for Spider Solitaire:', sameCategory.length);
sameCategory.forEach((game, i) => {
    console.log(`  ${i+1}. ${game.name} (${game.url})`);
});
