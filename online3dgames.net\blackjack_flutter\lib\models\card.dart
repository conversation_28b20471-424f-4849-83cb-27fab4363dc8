enum Suit { spades, hearts, diamonds, clubs }

enum CardValue { 
  ace, two, three, four, five, six, seven, eight, nine, ten, jack, queen, king 
}

class PlayingCard {
  final Suit suit;
  final CardValue value;
  final bool isHidden;

  const PlayingCard({
    required this.suit,
    required this.value,
    this.isHidden = false,
  });

  // 获取卡牌颜色
  CardColor get color {
    return (suit == Suit.hearts || suit == Suit.diamonds) 
        ? CardColor.red 
        : CardColor.black;
  }

  // 获取卡牌数值（用于计分）
  int get numericValue {
    switch (value) {
      case CardValue.ace:
        return 11; // 默认为11，在计分时会动态调整
      case CardValue.two:
        return 2;
      case CardValue.three:
        return 3;
      case CardValue.four:
        return 4;
      case CardValue.five:
        return 5;
      case CardValue.six:
        return 6;
      case CardValue.seven:
        return 7;
      case CardValue.eight:
        return 8;
      case CardValue.nine:
        return 9;
      case CardValue.ten:
      case CardValue.jack:
      case CardValue.queen:
      case CardValue.king:
        return 10;
    }
  }

  // 获取卡牌显示文本
  String get displayValue {
    switch (value) {
      case CardValue.ace:
        return 'A';
      case CardValue.two:
        return '2';
      case CardValue.three:
        return '3';
      case CardValue.four:
        return '4';
      case CardValue.five:
        return '5';
      case CardValue.six:
        return '6';
      case CardValue.seven:
        return '7';
      case CardValue.eight:
        return '8';
      case CardValue.nine:
        return '9';
      case CardValue.ten:
        return '10';
      case CardValue.jack:
        return 'J';
      case CardValue.queen:
        return 'Q';
      case CardValue.king:
        return 'K';
    }
  }

  // 获取花色符号
  String get suitSymbol {
    switch (suit) {
      case Suit.spades:
        return '♠';
      case Suit.hearts:
        return '♥';
      case Suit.diamonds:
        return '♦';
      case Suit.clubs:
        return '♣';
    }
  }

  // 创建隐藏卡牌
  PlayingCard copyWithHidden(bool hidden) {
    return PlayingCard(
      suit: suit,
      value: value,
      isHidden: hidden,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PlayingCard &&
        other.suit == suit &&
        other.value == value;
  }

  @override
  int get hashCode => suit.hashCode ^ value.hashCode;

  @override
  String toString() {
    return '${displayValue}${suitSymbol}';
  }
}

enum CardColor { red, black }

// 计算手牌分数的工具函数
class CardUtils {
  static int calculateScore(List<PlayingCard> cards) {
    if (cards.isEmpty) return 0;
    
    int score = 0;
    int aces = 0;
    
    for (final card in cards) {
      if (card.value == CardValue.ace) {
        aces++;
        score += 11;
      } else {
        score += card.numericValue;
      }
    }
    
    // 调整A的值（从11变为1）
    while (score > 21 && aces > 0) {
      score -= 10;
      aces--;
    }
    
    return score;
  }
  
  static bool isBlackjack(List<PlayingCard> cards) {
    return cards.length == 2 && calculateScore(cards) == 21;
  }
  
  static bool isBust(List<PlayingCard> cards) {
    return calculateScore(cards) > 21;
  }
  
  static bool isSoft(List<PlayingCard> cards) {
    int score = 0;
    bool hasAce = false;
    
    for (final card in cards) {
      if (card.value == CardValue.ace) {
        hasAce = true;
        score += 11;
      } else {
        score += card.numericValue;
      }
    }
    
    return hasAce && score <= 21;
  }
}
