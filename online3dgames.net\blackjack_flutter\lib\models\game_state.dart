import 'card.dart';
import 'player.dart';

enum GamePhase {
  waiting,    // 等待开始
  betting,    // 下注阶段
  dealing,    // 发牌阶段
  playing,    // 玩家操作阶段
  dealer,     // 庄家操作阶段
  finished,   // 游戏结束
}

enum GameResult {
  win,
  lose,
  push,
  blackjack,
  bust,
  surrender,
}

class GameSettings {
  final int deckCount;
  final bool dealerHitsSoft17;
  final bool surrenderAllowed;
  final bool doubleAfterSplitAllowed;
  final bool insuranceAllowed;
  final double blackjackPayout;
  final int minBet;
  final int maxBet;

  const GameSettings({
    this.deckCount = 6,
    this.dealerHitsSoft17 = true,
    this.surrenderAllowed = true,
    this.doubleAfterSplitAllowed = true,
    this.insuranceAllowed = true,
    this.blackjackPayout = 1.5,
    this.minBet = 5,
    this.maxBet = 500,
  });
}

class GameState {
  final List<PlayingCard> deck;
  final List<PlayingCard> discardPile;
  final Player dealer;
  final Player humanPlayer;
  final GamePhase phase;
  final GameSettings settings;
  final int balance;
  final bool insuranceOffered;
  final String? statusMessage;
  final bool isShuffling;

  const GameState({
    required this.deck,
    required this.discardPile,
    required this.dealer,
    required this.humanPlayer,
    required this.phase,
    required this.settings,
    required this.balance,
    this.insuranceOffered = false,
    this.statusMessage,
    this.isShuffling = false,
  });

  // 创建初始游戏状态
  factory GameState.initial() {
    return GameState(
      deck: [],
      discardPile: [],
      dealer: Player(
        id: 'dealer',
        name: 'Dealer',
        type: PlayerType.dealer,
      ),
      humanPlayer: Player(
        id: 'human',
        name: 'You',
        type: PlayerType.human,
        isActive: true,
      ),
      phase: GamePhase.waiting,
      settings: const GameSettings(),
      balance: 1000,
    );
  }

  // 游戏是否进行中
  bool get isGameInProgress {
    return phase == GamePhase.dealing || 
           phase == GamePhase.playing || 
           phase == GamePhase.dealer;
  }

  // 是否可以下注
  bool get canBet {
    return phase == GamePhase.betting && !isGameInProgress;
  }

  // 是否可以开始游戏
  bool get canStartGame {
    return phase == GamePhase.betting && humanPlayer.bet > 0;
  }

  // 庄家是否有21点
  bool get dealerHasBlackjack {
    return dealer.isBlackjack;
  }

  // 玩家是否有21点
  bool get playerHasBlackjack {
    return humanPlayer.isBlackjack;
  }

  // 是否应该提供保险
  bool get shouldOfferInsurance {
    return settings.insuranceAllowed &&
           dealer.cards.isNotEmpty &&
           dealer.cards.first.value == CardValue.ace &&
           !playerHasBlackjack;
  }

  // 牌组剩余卡牌数量
  int get remainingCards {
    return deck.length;
  }

  // 是否需要洗牌
  bool get needsShuffle {
    return deck.length < 20; // 当剩余卡牌少于20张时洗牌
  }

  // 复制状态
  GameState copyWith({
    List<PlayingCard>? deck,
    List<PlayingCard>? discardPile,
    Player? dealer,
    Player? humanPlayer,
    GamePhase? phase,
    GameSettings? settings,
    int? balance,
    bool? insuranceOffered,
    String? statusMessage,
    bool? isShuffling,
  }) {
    return GameState(
      deck: deck ?? this.deck,
      discardPile: discardPile ?? this.discardPile,
      dealer: dealer ?? this.dealer,
      humanPlayer: humanPlayer ?? this.humanPlayer,
      phase: phase ?? this.phase,
      settings: settings ?? this.settings,
      balance: balance ?? this.balance,
      insuranceOffered: insuranceOffered ?? this.insuranceOffered,
      statusMessage: statusMessage ?? this.statusMessage,
      isShuffling: isShuffling ?? this.isShuffling,
    );
  }

  @override
  String toString() {
    return 'GameState(phase: $phase, balance: $balance, deck: ${deck.length} cards)';
  }
}

// 游戏结果数据
class HandResult {
  final List<PlayingCard> hand;
  final int score;
  final GameResult result;
  final int bet;
  final int winnings;
  final String message;

  const HandResult({
    required this.hand,
    required this.score,
    required this.result,
    required this.bet,
    required this.winnings,
    required this.message,
  });
}

class GameRoundResult {
  final List<HandResult> playerResults;
  final HandResult? dealerResult;
  final int totalWinnings;
  final int totalBet;
  final bool hasInsurance;
  final int insurancePayout;

  const GameRoundResult({
    required this.playerResults,
    this.dealerResult,
    required this.totalWinnings,
    required this.totalBet,
    this.hasInsurance = false,
    this.insurancePayout = 0,
  });

  int get netResult => totalWinnings - totalBet + insurancePayout;
}
