#!/usr/bin/env node

// Game Page Template Generator
// Automatically generates complete game page templates with SEO optimization

const fs = require('fs');
const path = require('path');

// Configuration
const BASE_DIR = path.dirname(__dirname);

// HTML template for game pages
function generateGamePageTemplate(gameConfig) {
    const {
        name,
        icon,
        description,
        url,
        category,
        seoTitle,
        seoDescription,
        tags = [],
        rating = '4.5'
    } = gameConfig;
    
    const gameKey = url.substring(1); // Remove leading slash
    const keywords = [name.toLowerCase(), ...tags.map(tag => tag.toLowerCase()), 'online game', 'free game', 'browser game'].join(', ');
    
    return `<!DOCTYPE html>
<html lang="en">
<head>
<!-- Google tag (gtag.js) -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-WZL8VKTQ0M"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-WZL8VKTQ0M');
</script>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${seoTitle}</title>
    <meta name="description" content="${seoDescription}">
    <meta name="keywords" content="${keywords}">
    <meta name="author" content="Flow Fray">
    
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <meta name="theme-color" content="#1a4d2e">
    
    <!-- Open Graph Tags -->
    <meta property="og:title" content="${name} - Flow Fray">
    <meta property="og:description" content="${seoDescription}">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://flowfray.com${url}">
    
    <!-- Twitter Card Tags -->
    <meta name="twitter:card" content="summary">
    <meta name="twitter:title" content="${name} - Flow Fray">
    <meta name="twitter:description" content="${seoDescription}">
    
    <!-- Canonical URL -->
    <link rel="canonical" href="https://flowfray.com${url}">
    
    <!-- Preload critical resources -->
    <link rel="preload" href="/${gameKey}/css/styles.css" as="style">
    <link rel="stylesheet" href="/${gameKey}/css/styles.css">
    <link rel="stylesheet" href="/assets/css/game-recommendations.css">
    <link rel="stylesheet" href="/assets/css/common-buttons.css">
    
    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Game",
        "name": "${name}",
        "description": "${seoDescription}",
        "url": "https://flowfray.com${url}",
        "genre": "${category}",
        "gamePlatform": "Web Browser",
        "operatingSystem": "Any",
        "applicationCategory": "Game",
        "aggregateRating": {
            "@type": "AggregateRating",
            "ratingValue": "${rating}",
            "ratingCount": "1000"
        }
    }
    </script>
</head>
<body>
    <!-- Mobile Landscape Prompt -->
    <div id="landscape-prompt" class="landscape-prompt">
        <div class="landscape-content">
            <div class="rotate-icon">📱</div>
            <h2>Better Experience in Landscape</h2>
            <p>Please rotate your device to landscape mode for the best ${name} experience</p>
        </div>
    </div>

    <!-- Game Container -->
    <div class="game-container">
        <!-- Game Header -->
        <header class="game-header">
            <div class="header-left">
                <h1>${name}</h1>
                <div class="game-info">
                    <div class="game-status">
                        <span class="info-label">Status:</span>
                        <span id="gameStatus">Ready</span>
                    </div>
                    <div class="game-score">
                        <span class="info-label">Score:</span>
                        <span id="gameScore">0</span>
                    </div>
                </div>
            </div>
            
            <div class="header-right">
                <button id="fullscreen-button" class="icon-button" title="Toggle Fullscreen">
                    <span class="icon">⛶</span>
                </button>
                <button id="rules-button" class="icon-button" title="Game Rules">
                    <span class="icon">❓</span>
                </button>
                <button id="settings-button" class="icon-button" title="Settings">
                    <span class="icon">⚙️</span>
                </button>
            </div>
        </header>

        <!-- Game Area -->
        <main class="game-area">
            <div class="game-board" id="gameBoard">
                <!-- Game content will be inserted here -->
                <div class="game-placeholder">
                    <div class="placeholder-icon">${icon}</div>
                    <h2>${name}</h2>
                    <p>${description}</p>
                    <button id="startGame" class="start-button">Start Game</button>
                </div>
            </div>
        </main>

        <!-- Game Controls -->
        <div class="game-controls">
            <button id="newGame" class="control-button">New Game</button>
            <button id="pauseGame" class="control-button">Pause</button>
            <button id="hintButton" class="control-button">Hint</button>
            <button id="undoMove" class="control-button">Undo</button>
        </div>
    </div>

    <!-- Rules Modal -->
    <div id="rulesModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>${name} Rules</h2>
                <button class="close-button" id="closeRules">&times;</button>
            </div>
            <div class="modal-body">
                <h3>How to Play</h3>
                <p>${description}</p>
                
                <h3>Game Rules</h3>
                <ul>
                    <li>Rule 1: [Add specific game rules here]</li>
                    <li>Rule 2: [Add specific game rules here]</li>
                    <li>Rule 3: [Add specific game rules here]</li>
                </ul>
                
                <h3>Scoring</h3>
                <p>Points are awarded based on [add scoring system here].</p>
                
                <h3>Tips</h3>
                <ul>
                    <li>Tip 1: [Add helpful tips here]</li>
                    <li>Tip 2: [Add helpful tips here]</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Settings Modal -->
    <div id="settingsModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Settings</h2>
                <button class="close-button" id="closeSettings">&times;</button>
            </div>
            <div class="modal-body">
                <div class="setting-group">
                    <label for="soundEnabled">Sound Effects:</label>
                    <input type="checkbox" id="soundEnabled" checked>
                </div>
                
                <div class="setting-group">
                    <label for="musicEnabled">Background Music:</label>
                    <input type="checkbox" id="musicEnabled" checked>
                </div>
                
                <div class="setting-group">
                    <label for="difficulty">Difficulty:</label>
                    <select id="difficulty">
                        <option value="easy">Easy</option>
                        <option value="medium" selected>Medium</option>
                        <option value="hard">Hard</option>
                    </select>
                </div>
                
                <div class="setting-group">
                    <label for="animations">Animations:</label>
                    <input type="checkbox" id="animations" checked>
                </div>
            </div>
        </div>
    </div>

    <!-- SEO Content Section -->
    <div class="seo-content-section">
        <div class="seo-content">
            <h2>About ${name}</h2>
            <p>${description}</p>
            
            <h3>Features</h3>
            <ul>
                <li>Free to play online</li>
                <li>No downloads required</li>
                <li>Mobile-friendly design</li>
                <li>Multiple difficulty levels</li>
                <li>Save progress automatically</li>
            </ul>
            
            <h3>How to Play ${name}</h3>
            <p>Getting started with ${name} is easy! Simply click the "Start Game" button above and follow the on-screen instructions. The game features intuitive controls and helpful hints to guide you through your first few rounds.</p>
            
            <h3>Tips for Success</h3>
            <p>Master ${name} by practicing regularly and experimenting with different strategies. Use the hint system when you're stuck, and don't be afraid to try new approaches to improve your skills.</p>
        </div>
    </div>

    <!-- Game Recommendations will be inserted here by the update script -->

    <!-- Scripts -->
    <script src="/assets/js/jquery-3.7.1.min.js"></script>
    <script src="/${gameKey}/js/game.js"></script>
    <script src="/assets/js/game-recommendations.js"></script>
</body>
</html>`;
}

// CSS template for game pages
function generateGameCSSTemplate(gameConfig) {
    const { name, icon } = gameConfig;
    
    return `/* ${name} Game Styles */

/* Base Game Container */
.game-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background: linear-gradient(135deg, #1a4d2e 0%, #2d5a3d 100%);
    min-height: 100vh;
    color: white;
    font-family: 'Arial', sans-serif;
}

/* Game Header */
.game-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 0;
    border-bottom: 2px solid rgba(255, 255, 255, 0.2);
    margin-bottom: 20px;
}

.header-left h1 {
    margin: 0;
    font-size: 2.5em;
    color: #fff;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.game-info {
    display: flex;
    gap: 20px;
    margin-top: 10px;
}

.info-label {
    font-weight: bold;
    color: #a0d4a0;
}

.header-right {
    display: flex;
    gap: 10px;
}

.icon-button {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.icon-button:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

/* Game Area */
.game-area {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 20px;
    min-height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.game-board {
    width: 100%;
    height: 100%;
    position: relative;
}

.game-placeholder {
    text-align: center;
    padding: 40px;
}

.placeholder-icon {
    font-size: 4em;
    margin-bottom: 20px;
}

.start-button {
    background: linear-gradient(45deg, #4CAF50, #45a049);
    color: white;
    border: none;
    padding: 15px 30px;
    font-size: 1.2em;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 20px;
}

.start-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
}

/* Game Controls */
.game-controls {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 30px;
}

.control-button {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    padding: 10px 20px;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.control-button:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
}

.control-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
}

.modal-content {
    background: linear-gradient(135deg, #2d5a3d 0%, #1a4d2e 100%);
    margin: 5% auto;
    padding: 0;
    border-radius: 15px;
    width: 90%;
    max-width: 600px;
    color: white;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 30px;
    border-bottom: 2px solid rgba(255, 255, 255, 0.2);
}

.modal-header h2 {
    margin: 0;
    color: #fff;
}

.close-button {
    background: none;
    border: none;
    color: white;
    font-size: 2em;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-button:hover {
    color: #ff6b6b;
}

.modal-body {
    padding: 30px;
}

.setting-group {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 10px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.setting-group:last-child {
    border-bottom: none;
}

.setting-group label {
    font-weight: bold;
    color: #a0d4a0;
}

.setting-group input,
.setting-group select {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 5px 10px;
    border-radius: 5px;
}

/* SEO Content Section */
.seo-content-section {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 30px;
}

.seo-content h2,
.seo-content h3 {
    color: #a0d4a0;
    margin-top: 0;
}

.seo-content ul {
    padding-left: 20px;
}

.seo-content li {
    margin-bottom: 8px;
    color: #e0e0e0;
}

/* Landscape Prompt */
.landscape-prompt {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    z-index: 10000;
    justify-content: center;
    align-items: center;
}

.landscape-content {
    text-align: center;
    color: white;
    padding: 40px;
}

.rotate-icon {
    font-size: 4em;
    margin-bottom: 20px;
    animation: rotate 2s infinite linear;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .game-container {
        padding: 10px;
    }
    
    .game-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .header-left h1 {
        font-size: 2em;
    }
    
    .game-area {
        padding: 20px;
        min-height: 300px;
    }
    
    .game-controls {
        flex-wrap: wrap;
        gap: 10px;
    }
    
    .control-button {
        padding: 8px 15px;
        font-size: 0.9em;
    }
    
    .modal-content {
        width: 95%;
        margin: 10% auto;
    }
    
    .modal-body {
        padding: 20px;
    }
}

/* Portrait orientation on mobile */
@media (max-width: 768px) and (orientation: portrait) {
    .landscape-prompt {
        display: flex;
    }
}`;
}

// JavaScript template for game pages
function generateGameJSTemplate(gameConfig) {
    const { name } = gameConfig;
    
    return `// ${name} Game Logic
// This file contains the main game logic and event handlers

class ${name.replace(/\s+/g, '')}Game {
    constructor() {
        this.gameState = 'ready';
        this.score = 0;
        this.isGameActive = false;
        this.settings = {
            soundEnabled: true,
            musicEnabled: true,
            difficulty: 'medium',
            animations: true
        };
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.loadSettings();
        this.updateUI();
    }
    
    setupEventListeners() {
        // Game controls
        $('#startGame').on('click', () => this.startGame());
        $('#newGame').on('click', () => this.newGame());
        $('#pauseGame').on('click', () => this.pauseGame());
        $('#hintButton').on('click', () => this.showHint());
        $('#undoMove').on('click', () => this.undoMove());
        
        // Header buttons
        $('#fullscreen-button').on('click', () => this.toggleFullscreen());
        $('#rules-button').on('click', () => this.showRules());
        $('#settings-button').on('click', () => this.showSettings());
        
        // Modal controls
        $('#closeRules').on('click', () => this.hideRules());
        $('#closeSettings').on('click', () => this.hideSettings());
        
        // Settings
        $('#soundEnabled').on('change', (e) => this.updateSetting('soundEnabled', e.target.checked));
        $('#musicEnabled').on('change', (e) => this.updateSetting('musicEnabled', e.target.checked));
        $('#difficulty').on('change', (e) => this.updateSetting('difficulty', e.target.value));
        $('#animations').on('change', (e) => this.updateSetting('animations', e.target.checked));
        
        // Close modals when clicking outside
        $('.modal').on('click', (e) => {
            if (e.target === e.currentTarget) {
                this.hideRules();
                this.hideSettings();
            }
        });
        
        // Keyboard shortcuts
        $(document).on('keydown', (e) => this.handleKeyPress(e));
        
        // Fullscreen change detection
        $(document).on('fullscreenchange webkitfullscreenchange mozfullscreenchange MSFullscreenChange', () => {
            this.handleFullscreenChange();
        });
    }
    
    startGame() {
        this.gameState = 'playing';
        this.isGameActive = true;
        this.score = 0;
        
        // Hide placeholder and show game board
        $('.game-placeholder').hide();
        
        // Initialize game-specific logic here
        this.initializeGameBoard();
        
        this.updateUI();
        this.playSound('gameStart');
    }
    
    newGame() {
        this.gameState = 'ready';
        this.isGameActive = false;
        this.score = 0;
        
        // Reset game board
        this.resetGameBoard();
        
        // Show placeholder
        $('.game-placeholder').show();
        
        this.updateUI();
    }
    
    pauseGame() {
        if (this.gameState === 'playing') {
            this.gameState = 'paused';
            this.isGameActive = false;
        } else if (this.gameState === 'paused') {
            this.gameState = 'playing';
            this.isGameActive = true;
        }
        
        this.updateUI();
    }
    
    showHint() {
        if (!this.isGameActive) return;
        
        // Implement hint logic here
        console.log('Hint requested');
        this.playSound('hint');
    }
    
    undoMove() {
        if (!this.isGameActive) return;
        
        // Implement undo logic here
        console.log('Undo move');
        this.playSound('undo');
    }
    
    updateScore(points) {
        this.score += points;
        this.updateUI();
    }
    
    updateUI() {
        $('#gameStatus').text(this.gameState.charAt(0).toUpperCase() + this.gameState.slice(1));
        $('#gameScore').text(this.score);
        
        // Update button states
        $('#pauseGame').text(this.gameState === 'paused' ? 'Resume' : 'Pause');
        $('#pauseGame').prop('disabled', this.gameState === 'ready');
        $('#hintButton').prop('disabled', !this.isGameActive);
        $('#undoMove').prop('disabled', !this.isGameActive);
    }
    
    initializeGameBoard() {
        // Implement game-specific board initialization
        console.log('Initializing game board');
    }
    
    resetGameBoard() {
        // Implement game-specific board reset
        console.log('Resetting game board');
    }
    
    // Modal management
    showRules() {
        $('#rulesModal').show();
    }
    
    hideRules() {
        $('#rulesModal').hide();
    }
    
    showSettings() {
        // Update settings UI with current values
        $('#soundEnabled').prop('checked', this.settings.soundEnabled);
        $('#musicEnabled').prop('checked', this.settings.musicEnabled);
        $('#difficulty').val(this.settings.difficulty);
        $('#animations').prop('checked', this.settings.animations);
        
        $('#settingsModal').show();
    }
    
    hideSettings() {
        $('#settingsModal').hide();
        this.saveSettings();
    }
    
    updateSetting(key, value) {
        this.settings[key] = value;
        console.log(\`Setting updated: \${key} = \${value}\`);
    }
    
    loadSettings() {
        const saved = localStorage.getItem('${name.replace(/\s+/g, '')}Settings');
        if (saved) {
            this.settings = { ...this.settings, ...JSON.parse(saved) };
        }
    }
    
    saveSettings() {
        localStorage.setItem('${name.replace(/\s+/g, '')}Settings', JSON.stringify(this.settings));
    }
    
    // Fullscreen management
    toggleFullscreen() {
        if (!document.fullscreenElement) {
            document.documentElement.requestFullscreen().catch(console.error);
        } else {
            document.exitFullscreen().catch(console.error);
        }
    }
    
    handleFullscreenChange() {
        const isFullscreen = !!document.fullscreenElement;
        $('#fullscreen-button .icon').text(isFullscreen ? '⛶' : '⛶');
        
        // Hide/show SEO content in fullscreen
        if (isFullscreen) {
            $('.seo-content-section').hide();
            $('.game-recommendations').hide();
        } else {
            $('.seo-content-section').show();
            $('.game-recommendations').show();
        }
    }
    
    // Keyboard handling
    handleKeyPress(e) {
        if (e.target.tagName === 'INPUT' || e.target.tagName === 'SELECT') return;
        
        switch (e.key) {
            case 'Escape':
                this.hideRules();
                this.hideSettings();
                break;
            case 'F11':
                e.preventDefault();
                this.toggleFullscreen();
                break;
            case ' ':
                e.preventDefault();
                if (this.gameState === 'ready') {
                    this.startGame();
                } else {
                    this.pauseGame();
                }
                break;
            case 'h':
            case 'H':
                this.showHint();
                break;
            case 'u':
            case 'U':
                this.undoMove();
                break;
            case 'n':
            case 'N':
                this.newGame();
                break;
        }
    }
    
    // Sound management
    playSound(soundType) {
        if (!this.settings.soundEnabled) return;
        
        // Implement sound playing logic here
        console.log(\`Playing sound: \${soundType}\`);
    }
    
    // Game end handling
    gameOver(won = false) {
        this.gameState = won ? 'won' : 'lost';
        this.isGameActive = false;
        
        this.updateUI();
        this.playSound(won ? 'win' : 'lose');
        
        // Show game over message
        setTimeout(() => {
            alert(won ? 'Congratulations! You won!' : 'Game Over! Try again?');
        }, 500);
    }
}

// Initialize game when document is ready
$(document).ready(() => {
    window.game = new ${name.replace(/\s+/g, '')}Game();
    
    // Handle landscape prompt on mobile
    function checkOrientation() {
        if (window.innerWidth < 768 && window.innerHeight > window.innerWidth) {
            $('#landscape-prompt').show();
        } else {
            $('#landscape-prompt').hide();
        }
    }
    
    checkOrientation();
    $(window).on('resize orientationchange', checkOrientation);
});`;
}

// Main function to generate complete game template
function generateCompleteGameTemplate(gameConfig) {
    const gameKey = gameConfig.url.substring(1); // Remove leading slash
    const gameDir = path.join(BASE_DIR, gameKey);
    
    // Create directory structure
    const directories = [
        gameDir,
        path.join(gameDir, 'css'),
        path.join(gameDir, 'js'),
        path.join(gameDir, 'images'),
        path.join(gameDir, 'audio')
    ];
    
    directories.forEach(dir => {
        if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir, { recursive: true });
        }
    });
    
    // Generate files
    const files = [
        {
            path: path.join(gameDir, 'index.html'),
            content: generateGamePageTemplate(gameConfig)
        },
        {
            path: path.join(gameDir, 'css', 'styles.css'),
            content: generateGameCSSTemplate(gameConfig)
        },
        {
            path: path.join(gameDir, 'js', 'game.js'),
            content: generateGameJSTemplate(gameConfig)
        }
    ];
    
    files.forEach(file => {
        fs.writeFileSync(file.path, file.content, 'utf8');
        console.log(`✅ Generated: ${file.path}`);
    });
    
    console.log(`\n🎉 Complete game template generated for "${gameConfig.name}"`);
    console.log(`📁 Location: ${gameDir}`);
    console.log('\nNext steps:');
    console.log('1. Customize the game logic in js/game.js');
    console.log('2. Add game-specific styles to css/styles.css');
    console.log('3. Add game assets to images/ and audio/ folders');
    console.log('4. Test the game page');
    console.log('5. Run the deployment script to add to the system');
    
    return gameDir;
}

// Export functions
module.exports = {
    generateGamePageTemplate,
    generateGameCSSTemplate,
    generateGameJSTemplate,
    generateCompleteGameTemplate
};

// Command line interface
if (require.main === module) {
    const args = process.argv.slice(2);
    
    if (args.length === 0) {
        console.log('🎮 Game Page Template Generator\n');
        console.log('Usage: node game-page-generator.js config.json');
        console.log('\nExample config.json:');
        console.log(JSON.stringify({
            name: "My New Game",
            icon: "🎮",
            description: "An exciting new game experience",
            url: "/my-new-game",
            category: "puzzle",
            seoTitle: "My New Game Online - Free Puzzle Game",
            seoDescription: "Play My New Game online for free. An exciting puzzle experience.",
            tags: ["Puzzle", "Logic", "Fun"],
            rating: "4.5"
        }, null, 2));
        return;
    }
    
    const configPath = path.resolve(args[0]);
    if (!fs.existsSync(configPath)) {
        console.error(`❌ Config file not found: ${configPath}`);
        return;
    }
    
    try {
        const gameConfig = JSON.parse(fs.readFileSync(configPath, 'utf8'));
        generateCompleteGameTemplate(gameConfig);
    } catch (error) {
        console.error(`❌ Error reading config file: ${error.message}`);
    }
}
