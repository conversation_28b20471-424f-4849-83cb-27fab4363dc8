/**
 * Mahjong Solitaire - Layout System
 * Standard "Turtle" layout for Mahjong Solitaire
 */

class MahjongLayout {
    constructor() {
        this.gridRows = 12;
        this.gridCols = 20;
        this.maxLayers = 5;
        this.grid = [];
        this.layers = [];
    }

    /**
     * Initialize empty grid
     */
    initializeGrid() {
        this.grid = [];
        this.layers = [];

        // Initialize 2D grid
        for (let row = 0; row < this.gridRows; row++) {
            this.grid[row] = [];
            for (let col = 0; col < this.gridCols; col++) {
                this.grid[row][col] = null;
            }
        }

        // Initialize 3D layers
        for (let layer = 0; layer < this.maxLayers; layer++) {
            this.layers[layer] = [];
            for (let row = 0; row < this.gridRows; row++) {
                this.layers[layer][row] = [];
                for (let col = 0; col < this.gridCols; col++) {
                    this.layers[layer][row][col] = null;
                }
            }
        }
    }

    /**
     * Generate classic turtle layout positions (fixed and solvable)
     */
    generateTurtleLayout() {
        const positions = [];

        // Use classic fixed turtle layout that's known to be solvable
        const layoutPattern = this.getClassicTurtlePattern();

        return this.generatePositionsFromPattern(layoutPattern);
    }

    /**
     * Classic turtle pattern
     */
    getClassicTurtlePattern() {
        return [
            // Layer 0
            {
                layer: 0,
                blocks: [

                    { startRow: 2, startCol: 2, width: 2, height: 2 },
                    { startRow: 2, startCol: 5, width: 2, height: 2 },
                    { startRow: 2, startCol: 8, width: 2, height: 2 },
                    { startRow: 2, startCol: 11, width: 2, height: 2 },
                    { startRow: 4, startCol: 1, width: 2, height: 2 },
                    { startRow: 4, startCol: 4, width: 2, height: 2 },
                    { startRow: 4, startCol: 7, width: 2, height: 2 },
                    { startRow: 4, startCol: 10, width: 2, height: 2 },
                    { startRow: 4, startCol: 13, width: 2, height: 2 },
                    { startRow: 6, startCol: 2, width: 2, height: 2 },
                    { startRow: 6, startCol: 5, width: 2, height: 2 },
                    { startRow: 6, startCol: 8, width: 2, height: 2 },
                    { startRow: 6, startCol: 11, width: 2, height: 2 },

                    { startRow: 1, startCol: 6, width: 1, height: 1 },
                    { startRow: 1, startCol: 8, width: 1, height: 1 },
                    { startRow: 8, startCol: 6, width: 1, height: 1 },
                    { startRow: 8, startCol: 8, width: 1, height: 1 }
                ]
            },
            // Layer 1
            {
                layer: 1,
                blocks: [
                    { startRow: 3, startCol: 3, width: 2, height: 2 },
                    { startRow: 3, startCol: 6, width: 2, height: 2 },
                    { startRow: 3, startCol: 9, width: 2, height: 2 },
                    { startRow: 5, startCol: 2, width: 2, height: 2 },
                    { startRow: 5, startCol: 5, width: 2, height: 2 },
                    { startRow: 5, startCol: 8, width: 2, height: 2 },
                    { startRow: 5, startCol: 11, width: 2, height: 2 }
                ]
            },
            // Layer 2
            {
                layer: 2,
                blocks: [
                    { startRow: 4, startCol: 4, width: 2, height: 1 },
                    { startRow: 4, startCol: 7, width: 2, height: 1 },
                    { startRow: 4, startCol: 10, width: 2, height: 1 }
                ]
            },
            // Layer 3
            {
                layer: 3,
                blocks: [
                    { startRow: 4, startCol: 6, width: 1, height: 1 },
                    { startRow: 4, startCol: 8, width: 1, height: 1 }
                ]
            }
        ];
    }



    /**
     * Generate positions from layout pattern
     */
    generatePositionsFromPattern(layoutPattern) {
        const positions = [];

        layoutPattern.forEach(layerData => {
            layerData.blocks.forEach(block => {
                for (let r = 0; r < block.height; r++) {
                    for (let c = 0; c < block.width; c++) {
                        const row = block.startRow + r;
                        const col = block.startCol + c;

                        if (row >= 0 && row < this.gridRows &&
                            col >= 0 && col < this.gridCols) {
                            positions.push({
                                row: row,
                                col: col,
                                layer: layerData.layer
                            });
                        }
                    }
                }
            });
        });

        return positions;
    }

    /**
     * Place tiles in the layout
     */
    placeTiles(tiles) {
        this.initializeGrid();
        const positions = this.generateTurtleLayout();

        // Shuffle tiles for randomness
        this.shuffleTiles(tiles);

        // Place tiles in positions
        const maxTiles = Math.min(tiles.length, positions.length);
        for (let i = 0; i < maxTiles; i++) {
            const tile = tiles[i];
            const pos = positions[i];

            tile.row = pos.row;
            tile.col = pos.col;
            tile.layer = pos.layer;

            // Place in grid
            this.grid[pos.row][pos.col] = tile;
            this.layers[pos.layer][pos.row][pos.col] = tile;
        }

        // Remove excess tiles
        if (tiles.length > maxTiles) {
            tiles.splice(maxTiles);
        }

        this.updateBlockedStatus(tiles);
        return tiles;
    }

    /**
     * Shuffle tiles array for randomness
     */
    shuffleTiles(tiles) {
        for (let i = tiles.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [tiles[i], tiles[j]] = [tiles[j], tiles[i]];
        }
    }

    /**
     * Update blocked status for all tiles
     */
    updateBlockedStatus(tiles) {
        tiles.forEach(tile => {
            if (tile.removed) return;
            
            tile.blocked = this.isTileBlocked(tile);
        });
    }

    /**
     * Check if a tile is blocked (cannot be selected)

     */
    isTileBlocked(tile) {
        const topFree = this.isTopFree(tile);
        if (!topFree) {
            return true;
        }

        const leftFree = this.isSideFree(tile, 'left');
        const rightFree = this.isSideFree(tile, 'right');
        const isEdgeTile = tile.col === 0 || tile.col === this.gridCols - 1;

        return !(leftFree || rightFree || isEdgeTile);
    }

    /**
     * Check if a side of the tile is free

     */
    isSideFree(tile, side) {
        const checkCol = side === 'left' ? tile.col - 1 : tile.col + 1;

        if (checkCol < 0 || checkCol >= this.gridCols) {
            return true;
        }

        const blockingTile = this.layers[tile.layer][tile.row][checkCol];
        if (!blockingTile || blockingTile.removed) {
            return true;
        }
        return false;
    }

    /**
     * Check if the top of the tile is free
     */
    isTopFree(tile) {
        // Check all layers above
        for (let layer = tile.layer + 1; layer < this.maxLayers; layer++) {
            const tileAbove = this.layers[layer][tile.row][tile.col];
            if (tileAbove && !tileAbove.removed) {
                return false;
            }
        }
        return true;
    }

    /**
     * Get tile at specific position
     */
    getTileAt(row, col, layer = null) {
        if (layer !== null) {
            return this.layers[layer][row] && this.layers[layer][row][col];
        }
        return this.grid[row] && this.grid[row][col];
    }

    /**
     * Remove tile from layout
     */
    removeTile(tile) {
        if (this.grid[tile.row] && this.grid[tile.row][tile.col] === tile) {
            this.grid[tile.row][tile.col] = null;
        }
        if (this.layers[tile.layer] && this.layers[tile.layer][tile.row] && 
            this.layers[tile.layer][tile.row][tile.col] === tile) {
            this.layers[tile.layer][tile.row][tile.col] = null;
        }
    }
}
