import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';

class ChipWidget extends StatelessWidget {
  final int value;
  final double size;
  final bool isSelected;
  final VoidCallback? onTap;
  final bool isAnimated;

  const ChipWidget({
    super.key,
    required this.value,
    this.size = 50,
    this.isSelected = false,
    this.onTap,
    this.isAnimated = false,
  });

  @override
  Widget build(BuildContext context) {
    Widget chipWidget = _buildChip();

    if (isAnimated) {
      chipWidget = chipWidget
          .animate()
          .scale(begin: const Offset(0.8, 0.8), duration: 200.ms)
          .fadeIn(duration: 200.ms);
    }

    return GestureDetector(
      onTap: onTap,
      child: chipWidget,
    );
  }

  Widget _buildChip() {
    final chipColor = _getChipColor(value);
    final textColor = _getTextColor(value);

    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: RadialGradient(
          center: const Alignment(-0.3, -0.3),
          radius: 0.8,
          colors: [
            chipColor.withOpacity(0.9),
            chipColor,
            chipColor.withOpacity(0.7),
          ],
          stops: const [0.0, 0.7, 1.0],
        ),
        border: Border.all(
          color: isSelected ? Colors.yellow : Colors.white,
          width: isSelected ? 3 : 2,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.3),
            blurRadius: 6,
            offset: const Offset(2, 2),
          ),
          if (isSelected)
            BoxShadow(
              color: Colors.yellow.withOpacity(0.5),
              blurRadius: 8,
              offset: Offset.zero,
            ),
        ],
      ),
      child: Stack(
        children: [
          // 内圈装饰
          Center(
            child: Container(
              width: size * 0.7,
              height: size * 0.7,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: Colors.white.withOpacity(0.3),
                  width: 1,
                ),
              ),
            ),
          ),
          
          // 数值文本
          Center(
            child: Text(
              _formatChipValue(value),
              style: TextStyle(
                color: textColor,
                fontSize: size * 0.25,
                fontWeight: FontWeight.bold,
                shadows: [
                  Shadow(
                    color: Colors.black.withOpacity(0.5),
                    blurRadius: 2,
                    offset: const Offset(1, 1),
                  ),
                ],
              ),
            ),
          ),
          
          // 高光效果
          Positioned(
            top: size * 0.15,
            left: size * 0.15,
            child: Container(
              width: size * 0.3,
              height: size * 0.3,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                gradient: RadialGradient(
                  colors: [
                    Colors.white.withOpacity(0.4),
                    Colors.white.withOpacity(0.0),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color _getChipColor(int value) {
    switch (value) {
      case 1:
        return Colors.white;
      case 5:
        return Colors.red[600]!;
      case 10:
        return Colors.blue[600]!;
      case 25:
        return Colors.green[600]!;
      case 50:
        return Colors.orange[600]!;
      case 100:
        return Colors.black;
      case 500:
        return Colors.purple[600]!;
      default:
        return Colors.grey[600]!;
    }
  }

  Color _getTextColor(int value) {
    switch (value) {
      case 1:
        return Colors.black;
      case 100:
        return Colors.white;
      default:
        return Colors.white;
    }
  }

  String _formatChipValue(int value) {
    if (value >= 1000) {
      return '${(value / 1000).toStringAsFixed(value % 1000 == 0 ? 0 : 1)}K';
    }
    return value.toString();
  }
}

// 飞行筹码动画组件
class FlyingChipWidget extends StatefulWidget {
  final int value;
  final Offset startPosition;
  final Offset endPosition;
  final Duration duration;
  final VoidCallback? onComplete;

  const FlyingChipWidget({
    super.key,
    required this.value,
    required this.startPosition,
    required this.endPosition,
    this.duration = const Duration(milliseconds: 800),
    this.onComplete,
  });

  @override
  State<FlyingChipWidget> createState() => _FlyingChipWidgetState();
}

class _FlyingChipWidgetState extends State<FlyingChipWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<Offset> _positionAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(duration: widget.duration, vsync: this);
    
    _positionAnimation = Tween<Offset>(
      begin: widget.startPosition,
      end: widget.endPosition,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.8,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));

    _controller.forward().then((_) {
      if (widget.onComplete != null) {
        widget.onComplete!();
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Positioned(
          left: _positionAnimation.value.dx,
          top: _positionAnimation.value.dy,
          child: Transform.scale(
            scale: _scaleAnimation.value,
            child: ChipWidget(
              value: widget.value,
              size: 40,
            ),
          ),
        );
      },
    );
  }
}

// 筹码堆叠组件
class ChipStackWidget extends StatelessWidget {
  final List<int> chipValues;
  final double chipSize;
  final double stackOffset;

  const ChipStackWidget({
    super.key,
    required this.chipValues,
    this.chipSize = 40,
    this.stackOffset = 3,
  });

  @override
  Widget build(BuildContext context) {
    if (chipValues.isEmpty) {
      return SizedBox(width: chipSize, height: chipSize);
    }

    return SizedBox(
      width: chipSize,
      height: chipSize + (chipValues.length - 1) * stackOffset,
      child: Stack(
        children: chipValues.asMap().entries.map((entry) {
          final index = entry.key;
          final value = entry.value;
          
          return Positioned(
            bottom: index * stackOffset,
            child: ChipWidget(
              value: value,
              size: chipSize,
            ),
          );
        }).toList(),
      ),
    );
  }
}

// 筹码选择器组件
class ChipSelectorWidget extends StatelessWidget {
  final List<int> availableChips;
  final int? selectedChip;
  final Function(int) onChipSelected;
  final bool enabled;

  const ChipSelectorWidget({
    super.key,
    required this.availableChips,
    this.selectedChip,
    required this.onChipSelected,
    this.enabled = true,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: availableChips.map((chipValue) {
          return ChipWidget(
            value: chipValue,
            size: 50,
            isSelected: selectedChip == chipValue,
            onTap: enabled ? () => onChipSelected(chipValue) : null,
          );
        }).toList(),
      ),
    );
  }
}
