import 'card.dart';

enum PlayerType { human, dealer }

class Player {
  final String id;
  final String name;
  final PlayerType type;
  List<PlayingCard> cards;
  List<List<PlayingCard>> splitHands;
  int currentHandIndex;
  int bet;
  int insuranceBet;
  bool hasInsurance;
  bool hasSurrendered;
  bool isActive;

  Player({
    required this.id,
    required this.name,
    required this.type,
    this.cards = const [],
    this.splitHands = const [],
    this.currentHandIndex = 0,
    this.bet = 0,
    this.insuranceBet = 0,
    this.hasInsurance = false,
    this.hasSurrendered = false,
    this.isActive = false,
  });

  // 获取当前手牌
  List<PlayingCard> get currentHand {
    if (splitHands.isNotEmpty && currentHandIndex < splitHands.length) {
      return splitHands[currentHandIndex];
    }
    return cards;
  }

  // 获取当前手牌分数
  int get currentScore {
    return CardUtils.calculateScore(currentHand);
  }

  // 获取所有手牌分数（包括分牌）
  List<int> get allScores {
    if (splitHands.isNotEmpty) {
      return splitHands.map((hand) => CardUtils.calculateScore(hand)).toList();
    }
    return [CardUtils.calculateScore(cards)];
  }

  // 是否可以分牌
  bool get canSplit {
    return cards.length == 2 && 
           cards[0].value == cards[1].value && 
           splitHands.isEmpty;
  }

  // 是否可以加倍
  bool get canDouble {
    return currentHand.length == 2 && !hasSurrendered;
  }

  // 是否可以投降
  bool get canSurrender {
    return cards.length == 2 && splitHands.isEmpty && !hasInsurance;
  }

  // 当前手牌是否爆牌
  bool get isBust {
    return CardUtils.isBust(currentHand);
  }

  // 当前手牌是否是21点
  bool get isBlackjack {
    return CardUtils.isBlackjack(currentHand);
  }

  // 是否是软牌（包含A且A算作11）
  bool get isSoft {
    return CardUtils.isSoft(currentHand);
  }

  // 添加卡牌到当前手牌
  void addCard(PlayingCard card) {
    if (splitHands.isNotEmpty && currentHandIndex < splitHands.length) {
      splitHands[currentHandIndex] = [...splitHands[currentHandIndex], card];
    } else {
      cards = [...cards, card];
    }
  }

  // 分牌
  void split() {
    if (!canSplit) return;
    
    splitHands = [
      [cards[0]],
      [cards[1]],
    ];
    cards = [];
    currentHandIndex = 0;
  }

  // 移动到下一个分牌手牌
  bool moveToNextSplitHand() {
    if (splitHands.isEmpty) return false;
    
    currentHandIndex++;
    return currentHandIndex < splitHands.length;
  }

  // 重置玩家状态
  void reset() {
    cards = [];
    splitHands = [];
    currentHandIndex = 0;
    bet = 0;
    insuranceBet = 0;
    hasInsurance = false;
    hasSurrendered = false;
    isActive = false;
  }

  // 清空手牌但保留下注
  void clearCards() {
    cards = [];
    splitHands = [];
    currentHandIndex = 0;
    hasInsurance = false;
    hasSurrendered = false;
  }

  // 复制玩家对象
  Player copyWith({
    String? id,
    String? name,
    PlayerType? type,
    List<PlayingCard>? cards,
    List<List<PlayingCard>>? splitHands,
    int? currentHandIndex,
    int? bet,
    int? insuranceBet,
    bool? hasInsurance,
    bool? hasSurrendered,
    bool? isActive,
  }) {
    return Player(
      id: id ?? this.id,
      name: name ?? this.name,
      type: type ?? this.type,
      cards: cards ?? this.cards,
      splitHands: splitHands ?? this.splitHands,
      currentHandIndex: currentHandIndex ?? this.currentHandIndex,
      bet: bet ?? this.bet,
      insuranceBet: insuranceBet ?? this.insuranceBet,
      hasInsurance: hasInsurance ?? this.hasInsurance,
      hasSurrendered: hasSurrendered ?? this.hasSurrendered,
      isActive: isActive ?? this.isActive,
    );
  }

  @override
  String toString() {
    return 'Player(id: $id, name: $name, type: $type, score: $currentScore, bet: $bet)';
  }
}
